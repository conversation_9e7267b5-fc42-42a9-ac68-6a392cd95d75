import React from "react";
import { useState, useEffect, useCallback } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { <PERSON><PERSON> } from "../../../components/ui/button";
import {
  Tabs,
  TabsContent,
  Ta<PERSON>List,
  TabsTrigger,
} from "../../../components/ui/tabs";
import NoAppointmentsImg from "../../../assets/images/NoAppointments.png";
import { FaAngleLeft } from "react-icons/fa6";
import { Card, CardContent } from "../../../components/ui/card";
import { ScrollArea } from "../../../components/ui/scroll-area";
import { Edit, Loader2 } from "lucide-react";
import DoctorImgPlaceholder from "../../../assets/images/ProfileImagePlaceHolder.png";
import PrescriptionDetailsFilled from "../../../components/appointments-details-components/PrescriptionFilled";
import PreQuestioner from "../../../components/appointments-details-components/PreQuestioner";
import LabsFilled from "../../../components/appointments-details-components/LabsFilled";
import ImagingFilled from "../../../components/appointments-details-components/ImagingFilled";
import MedicalCertificateFilled from "../../../components/appointments-details-components/MedicalCertificateFilled";
import DoctorProfileCard from "../../../components/patient-side-components/doctor-profile-card/DoctorProfileCard";
import PatientCompliance from "../../../components/appointments-details-components/PatientCompliance";
import { getAppointmentById_apiCalls } from "../../../api/api_calls/appointment_apiCalls";
import { useToast } from "../../../hooks/use-toast";
import { formatDateReadable, formatTime } from "../../../helpers/dateTimeHelpers";

const PatientAppointmentsDetails = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { toast } = useToast();

  // Get appointment data from location state with null safety
  const appointmentId = location.state?.appointmentId || location.state?.appointment?.id || null;

  // State management
  const [isMobile, setIsMobile] = useState(false);
  const [loading, setLoading] = useState(true);
  const [appointmentData, setAppointmentData] = useState(null);
  const [doctorData, setDoctorData] = useState(null);
  // Helper function to get appointment status from appointment data
  const getAppointmentStatus = () => {
    try {
      return appointmentData?.appointmentStatus?.toLowerCase() || "upcoming";
    } catch (error) {
      console.error("Error getting appointment status:", error);
      return "upcoming";
    }
  };

  // Helper function to calculate appointment duration in minutes
  const calculateDuration = (startDateTime, endDateTime) => {
    if (!startDateTime || !endDateTime) return null; // Return null if data not available

    try {
      const start = new Date(startDateTime);
      const end = new Date(endDateTime);

      // Check if dates are valid
      if (isNaN(start.getTime()) || isNaN(end.getTime())) return null;

      const durationMs = end - start;
      const durationMinutes = Math.round(durationMs / (1000 * 60));

      return durationMinutes > 0 ? durationMinutes.toString() : null;
    } catch (error) {
      console.error("Error calculating duration:", error);
      return null;
    }
  };
  const isMoreThan48Hours = (startDateTime) => {
    const appointmentStart = new Date(startDateTime);
    const now = new Date();
    const diffMs = appointmentStart.getTime() - now.getTime();
    const diffHours = diffMs / (1000 * 60 * 60);
    return diffHours > 48;
  };

  const isAppointmentActive = (startDateTime, endDateTime) => {
    const now = new Date();
    const start = new Date(startDateTime);
    const end = new Date(endDateTime);
    return now >= start && now <= end;
  };

  const shouldShowCancelButton =
    appointmentData?.startDateTime &&
    appointmentData?.appointmentStatus === 'upcoming'
      ? isMoreThan48Hours(appointmentData.startDateTime)
      : false;

  const shouldShowJoinButton =
    appointmentData?.startDateTime &&
    appointmentData?.endDateTime &&
    appointmentData?.appointmentStatus === 'upcoming'
      ? isAppointmentActive(
          appointmentData.startDateTime,
          appointmentData.endDateTime,
        )
      : false;
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
    };

    handleResize();
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  // Function to fetch appointment details
  const fetchappointmentData = useCallback(async () => {
    if (!appointmentId) {
      toast({
        title: "Error",
        description: "No appointment ID provided",
        variant: "destructive",
      });
      setLoading(false);
      return;
    }

    setLoading(true);
    try {
      const response = await getAppointmentById_apiCalls({ id: appointmentId });

      if (response?.data) {
        console.log("API Response:", response.data);
        console.log("Doctor data:", response.data.doctor);
        setAppointmentData(response.data);



        // Extract doctor data from appointment
        const doctor = response.data.doctor;
        const appointment = response.data;

        // Always set doctor data, even if some fields are missing
        setDoctorData({
          name: doctor ? `${doctor.firstName || ""} ${doctor.lastName || ""}`.trim() || "N/A" : "N/A",
          qualification: doctor?.qualification || "N/A",
          location: doctor?.country || "N/A",
          service: appointment.consultation?.serviceName || "N/A",
          specialty: doctor?.speciality?.join(", ") || "N/A",
          appointmentDate: appointment.startDateTime ? formatDateReadable(appointment.startDateTime) : "N/A",
          appointmentTime: appointment.startDateTime && appointment.endDateTime
            ? `${formatTime(appointment.startDateTime)} - ${formatTime(appointment.endDateTime)}`
            : "N/A",
          fee: appointment.consultation?.price || 0,
          isPaid: appointment.isPaid || false,
          hasInsurance: appointment.hasInsurance || false,
          imageUrl: doctor?.profilePicture?.url || DoctorImgPlaceholder,
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to fetch appointment details. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  }, [appointmentId, toast]);

  // Effect to fetch appointment details on component mount
  useEffect(() => {
    fetchappointmentData();
  }, [fetchappointmentData]);

  // Loading state
  if (loading) {
    return (
      <div className="w-full flex items-center justify-center min-h-[400px]">
        <div className="flex flex-col items-center gap-4">
          <Loader2 className="h-8 w-8 animate-spin" />
          <p className="text-gray-600">Loading appointment details...</p>
        </div>
      </div>
    );
  }

  // Error state - no appointment data
  if (!appointmentData) {
    return (
      <div className="w-full flex items-center justify-center min-h-[400px]">
        <div className="flex flex-col items-center gap-4">
          <p className="text-gray-600">Appointment not found</p>
          <Button onClick={() => navigate("/patient/appointment")} variant="outline">
            Go Back to Appointments
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full flex items-center justify-center flex-col space-y-4 md:space-y-6 px-4 sm:px-6 pb-6">
      <div className="w-full max-w-7xl">
        <div className="w-full ">
          <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between w-full gap-4 py-4 sm:py-8">
            <div className="flex flex-row items-center gap-2 sm:gap-4">
              <div
                className="w-[30px] h-[30px] rounded-md border-[1px] border-[#D9D9D9] border-solid items-center justify-center flex cursor-pointer hover:bg-gray-100"
                onClick={() => navigate("/patient/appointment")}
              >
                <FaAngleLeft />
              </div>
              <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4">
                <h3 className="text-[#1E1E1E] font-bold text-[18px] sm:text-[20px]">
                  Appointment Detail
                </h3>
                <h6 className="text-[#1E1E1E] font-medium text-[12px] sm:text-[14px]">
                  {doctorData?.appointmentTime || "N/A"}{" "}
                  <span className="text-[#1E1E1EB2]">
                    ({calculateDuration(appointmentData?.startDateTime, appointmentData?.endDateTime)} minutes)
                  </span>
                </h6>
              </div>
            </div>
            {(shouldShowJoinButton || shouldShowCancelButton) && (
          <div className="flex flex-row items-center justify-center gap-2 sm:gap-4 w-full sm:w-auto">
            {shouldShowCancelButton && (
              <Button
                variant="destructive"
                size={isMobile ? "sm" : "default"}
                className="text-[#ffffff] w-full sm:w-auto"
              >
                Cancel
              </Button>
            )}
            {shouldShowJoinButton && (
              <Button
                variant="primary"
                size={isMobile ? "sm" : "default"}
                className="w-full sm:w-auto"
              >
                Join With Twilio
              </Button>
            )}
          </div>
        )}
            {getAppointmentStatus() === "upcoming" && (
              <div className="flex flex-row items-center justify-center gap-2 sm:gap-4 w-full sm:w-auto">
                <Button
                  variant={"destructive"}
                  size={isMobile ? "sm" : "default"}
                  className="text-[#ffffff] w-full sm:w-auto"
                >
                  Cancel
                </Button>
                <Button
                  variant={"primary"}
                  size={isMobile ? "sm" : "default"}
                  className="w-full sm:w-auto"
                >
                  Join With Twilio
                </Button>
              </div>
            )}
          </div>
        </div>

        <DoctorProfileCard doctor={doctorData || {
          name: "N/A",
          qualification: "N/A",
          location: "N/A",
          service: "N/A",
          specialty: "N/A",
          appointmentDate: "N/A",
          appointmentTime: "N/A",
          fee: 0,
          isPaid: false,
          hasInsurance: false,
          imageUrl: DoctorImgPlaceholder,
        }} />

        {getAppointmentStatus() === "upcoming" ? (
          <div className="flex items-center justify-center flex-col w-full space-y-4 md:space-y-6">
            <div className="flex items-center justify-between w-full">
              <h2 className="text-xl sm:text-2xl font-bold text-[#1E1E1E] mb-2 md:mb-4 w-full px-2 sm:px-0">
                Pre-Questioner Detail
              </h2>
              <Button
                className="border-[#0052FD] text-[#0052FD] border-2 font-bold"
                variant={"outline"}
                onClick={() => navigate("/patient/confirm-questionnaire", {
                  state: {
                    appointmentId: appointmentId
                  }
                })}
              >
                <Edit />
                Edit
              </Button>
            </div>

            <Card className="w-full">
              <CardContent className="p-4 md:p-6">
                <PreQuestioner appointmentId={appointmentId} />
              </CardContent>
            </Card>
          </div>
        ) : null}

        {getAppointmentStatus() === "completed" ? (
          <div className="w-full overflow-hidden">
            <Tabs
              defaultValue="pre-questioner"
              className="w-full flex flex-col"
            >
              <ScrollArea className="w-full">
                <TabsList className="flex items-start justify-start mb-4 overflow-x-auto p-1">
                  <TabsTrigger value="pre-questioner">
                    Pre Questioner
                  </TabsTrigger>
                  <TabsTrigger value="prescription-filled">
                    Prescription
                  </TabsTrigger>
                  <TabsTrigger value="labs-filled">Labs</TabsTrigger>
                  <TabsTrigger value="imaging-filled">Imaging</TabsTrigger>
                  <TabsTrigger value="medical-certificates">
                    Medical Certificates
                  </TabsTrigger>
                  <TabsTrigger value="patient-compliance-filled">
                    Patient Compliance
                  </TabsTrigger>
                </TabsList>
              </ScrollArea>

              <Card className="w-full">
                <CardContent className="p-4 md:p-6">
                  <TabsContent value="pre-questioner">
                    <PreQuestioner appointmentId={appointmentId} />
                  </TabsContent>

                  <TabsContent value="labs-filled">
                    <LabsFilled appointmentId={appointmentId} />
                  </TabsContent>

                  <TabsContent value="prescription-filled">
                    <PrescriptionDetailsFilled />
                  </TabsContent>

                  <TabsContent value="patient-compliance-filled">
                    <PatientCompliance />
                  </TabsContent>

                  <TabsContent value="imaging-filled">
                    <ImagingFilled  />
                  </TabsContent>

                  <TabsContent value="medical-certificates">
                    <MedicalCertificateFilled appointmentId={appointmentId} />
                  </TabsContent>
                </CardContent>
              </Card>
            </Tabs>
          </div>
        ) : null}

        {getAppointmentStatus() === "canceled" ? (
          <div className="flex flex-col items-center justify-center w-full">
            <div className="flex items-center justify-center w-full p-4 sm:p-8 md:p-16">
              <img
                src={NoAppointmentsImg || "/placeholder.svg"}
                alt="No Appointments"
                className="h-auto w-full max-w-[300px] md:max-w-[338px]"
              />
            </div>
            <p className="text-center text-gray-500 mb-8">
              This appointment has been canceled
            </p>
          </div>
        ) : null}
      </div>
    </div>
  );
};

export default PatientAppointmentsDetails;
