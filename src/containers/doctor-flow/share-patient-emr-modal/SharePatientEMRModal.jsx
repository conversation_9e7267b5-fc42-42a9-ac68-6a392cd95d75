import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "../../../components/ui/dialog";
import { Checkbox } from "../../../components/ui/checkbox";
import { But<PERSON> } from "../../../components/ui/button";
import { Input } from "../../../components/ui/input";
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "../../../components/ui/avatar";
import { Search } from "lucide-react";
import DefaultAvatar from "../../../assets/images/DefaultAvatar.png";

const SharePatientEMRModal = ({ isOpen, onClose, doctors = [] }) => {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedDoctors, setSelectedDoctors] = useState([]);

  // Filter doctors based on search query
  const filteredDoctors = (doctors || []).filter(
    (doctor) =>
      doctor.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      doctor.specialization
        ?.toLowerCase()
        .includes(searchQuery.toLowerCase()) ||
      doctor.email?.toLowerCase().includes(searchQuery.toLowerCase()),
  );

  // Handle doctor selection
  const handleDoctorSelection = (doctorId) => {
    setSelectedDoctors((prev) => {
      if (prev.includes(doctorId)) {
        return prev.filter((id) => id !== doctorId);
      } else {
        return [...prev, doctorId];
      }
    });
  };

  // Handle share EMR
  const handleShareEMR = () => {
    if (selectedDoctors.length === 0) {
      return;
    }
    // TODO: Implement API call to share EMR with selected doctors
    console.log("Sharing EMR with doctors:", selectedDoctors);

    // Reset state and close modal
    setSelectedDoctors([]);
    setSearchQuery("");
    onClose();
  };

  // Reset state when modal closes
  const handleClose = () => {
    setSelectedDoctors([]);
    setSearchQuery("");
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[700px] p-0 bg-white rounded-2xl border-0 shadow-2xl">
        {/* Header */}
        <DialogHeader className="px-6 py-6 border-b border-gray-100">
          <div className="flex items-center justify-between">
            <DialogTitle className="text-2xl font-semibold text-[#1E1E1E] flex items-center gap-2">
              Share Patient EMR
            </DialogTitle>
          </div>
        </DialogHeader>

        {/* Search Section */}
        <div className="px-6 py-4 border-b border-gray-100">
          <div className="relative">
            <div className="absolute inset-y-0 left-3 flex items-center pointer-events-none">
              <Search className="h-5 w-5 text-gray-400" />
            </div>
            <Input
              type="text"
              placeholder="Search doctors by name, specialization, or email"
              className="pl-10"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </div>

        {/* Content */}
        <div className="px-6 py-4 max-h-[400px] overflow-y-auto">
          <div className="space-y-3">
            {filteredDoctors.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                No doctors found matching your search.
              </div>
            ) : (
              filteredDoctors.map((doctor) => (
                <div
                  key={doctor.id}
                  className="flex items-center justify-between p-4 border border-gray-200 rounded-xl hover:bg-gray-50 transition-colors"
                >
                  <div className="flex items-center gap-4">
                    <Checkbox
                      id={`doctor-${doctor.id}`}
                      checked={selectedDoctors.includes(doctor.id)}
                      onCheckedChange={() => handleDoctorSelection(doctor.id)}
                    />
                    <Avatar className="w-12 h-12 border-2 border-blue-600">
                      <AvatarImage
                        src={doctor.avatar || DefaultAvatar}
                        alt={doctor.name}
                      />
                      <AvatarFallback className="bg-blue-100 text-blue-600 font-semibold">
                        {doctor.name
                          .split(" ")
                          .map((n) => n[0])
                          .join("")}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <h3 className="font-semibold text-gray-900 text-lg">
                        {doctor.name}
                      </h3>
                      <p className="text-gray-600 text-sm">
                        {doctor.specialization}
                      </p>
                      <p className="text-gray-500 text-xs">{doctor.email}</p>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>

        {/* Footer */}
        <div className="px-6 py-4 border-t border-gray-100 flex items-center justify-between">
          <div className="text-sm text-gray-600">
            {selectedDoctors.length} doctor
            {selectedDoctors.length !== 1 ? "s" : ""} selected
          </div>
          <div className="flex gap-3">
            <Button variant="outline" onClick={handleClose}>
              Cancel
            </Button>
            <Button
              onClick={handleShareEMR}
              disabled={selectedDoctors.length === 0}
              className="bg-blue-600 hover:bg-blue-700"
            >
              Share EMR
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default SharePatientEMRModal;
