import { memo, useState, useEffect, useCallback } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { useSelector } from "react-redux";
import { useToast } from "../../../hooks/use-toast";
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "../../../components/ui/avatar";
import { But<PERSON> } from "../../../components/ui/button";
import {
  <PERSON><PERSON>,
  Ta<PERSON>Content,
  Ta<PERSON>List,
  TabsTrigger,
} from "../../../components/ui/tabs2";
import { Badge } from "../../../components/ui/badge";
import { Card, CardContent } from "../../../components/ui/card-without-border";
import { Mail, Phone, MapPin, Menu, Edit, Loader2 } from "lucide-react";
import { ReactComponent as ShareIcon } from "../../../assets/svgs/ShareBlue.svg";
import CancelledAppointmentIllistration from "../../../assets/svgs/CancelledAppointmentIllistration.svg";
import DefaultAvatar from "../../../assets/images/DefaultAvatar.png";
import PreQuestioner from "../../../components/appointments-details-components/PreQuestioner";
import PatientSOAP from "../../../components/appointments-details-components/PatientSOAP";
import Labs from "../../../components/appointments-details-components/Labs";
import Prescription from "../../../components/appointments-details-components/Prescription";
import ROS from "../../../components/appointments-details-components/ROS";
import PatientCompliance from "../../../components/appointments-details-components/PatientCompliance";
import Imaging from "../../../components/appointments-details-components/Imaging";
import MedicalCertificate from "../../../components/appointments-details-components/MedicalCertificate";
import Activity from "../../../components/appointments-details-components/Activity";
import PatientSOAPFilled from "../../../components/appointments-details-components/PatientSOAPFilled";
import LabsFilled from "../../../components/appointments-details-components/LabsFilled";
import ROSFilled from "../../../components/appointments-details-components/ROSFilled";
import ImagingFilled from "../../../components/appointments-details-components/ImagingFilled";
import MedicalCertificateFilled from "../../../components/appointments-details-components/MedicalCertificateFilled";
import LoadingSpinner from "../../../components/animations/LoadingSpinner";
import { getAppointmentById_apiCalls } from "../../../api/api_calls/appointment_apiCalls";

const APPOINTMENT_STATUS = {
  UPCOMING: "upcoming",
  COMPLETED: "completed",
  CANCELED: "cancelled",
};

const PatientProfile = memo(
  ({ patientData = {}, appointmentData = {}, handleViewProfile }) => (
    <Card className="mb-6 bg-[#F8F8F8]">
      <CardContent className="p-4 sm:p-6">
        <div className="grid grid-cols-1 gap-4 sm:gap-6 lg:grid-cols-2 xl:grid-cols-3">
          <div className="flex flex-col sm:flex-row items-start sm:items-center space-y-4 sm:space-y-0 sm:space-x-4">
            <Avatar className="h-16 w-16 sm:h-20 sm:w-20 mx-auto sm:mx-0">
              <AvatarImage
                src={patientData.profilePicture.url || DefaultAvatar}
                alt={`${patientData.firstName + " " + patientData.lastName || "Patient"}'s profile picture`}
              />
              <AvatarFallback className="text-lg">
                {patientData.firstName +
                  " " +
                  patientData.lastName
                    ?.split(" ")
                    .map((n) => n[0])
                    .join("") || "PT"}
              </AvatarFallback>
            </Avatar>
            <div className="text-center sm:text-left">
              <h3 className="text-lg sm:text-xl font-semibold text-gray-900">
                {`${patientData.firstName || ""} ${patientData.lastName || ""}`}
              </h3>
              <div className="space-y-1 text-sm text-gray-600">
                <p>Gender: {patientData.gender || "Not specified"}</p>
                <p>DOB: {patientData.dob || "Not specified"}</p>
              </div>
            </div>
          </div>
          <div className="space-y-3">
            <div className="flex items-center text-gray-700 text-sm">
              <Phone
                className="mr-2 h-4 w-4 flex-shrink-0"
                aria-hidden="true"
              />
              <span className="break-all">
                {patientData.contactNumber || "Not provided"}
              </span>
            </div>
            <div className="flex items-center text-gray-700 text-sm">
              <Mail className="mr-2 h-4 w-4 flex-shrink-0" aria-hidden="true" />
              <span className="break-all">
                {patientData.email || "Not provided"}
              </span>
            </div>
            <div className="flex items-start text-gray-700 text-sm">
              <MapPin
                className="mr-2 h-4 w-4 flex-shrink-0 mt-0.5"
                aria-hidden="true"
              />
              <span>{patientData.address || "Not provided"}</span>
            </div>
          </div>
          <div className="space-y-2">
            <p className="text-sm text-gray-700">
              <span className="font-medium">Country:</span>{" "}
              {patientData.country || "Not specified"}
            </p>
            <div className="text-sm">
              <span className="font-medium text-gray-700">
                Medical Concerns:
              </span>
              <Badge variant="secondary" className="ml-2">
                {patientData.medicalConcerns || "Not specified"}
              </Badge>
            </div>
            <div className="text-sm" onClick={handleViewProfile}>
              <span className="font-[600] text-[#0052FD] underline cursor-pointer">
                View Profile Detail
              </span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  ),
);

const AppointmentsDetails = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { toast } = useToast();
  const user = useSelector((state) => state.userReducer.user);

  // Get appointment ID from navigation state
  const appointmentId = location.state?.appointmentId;

  // State management
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [isEditingSOAP, setIsEditingSOAP] = useState(false);
  const [isEditingLabs, setIsEditingLabs] = useState(false);
  const [isEditingROS, setIsEditingROS] = useState(false);
  const [isEditingImaging, setIsEditingImaging] = useState(false);
  const [isEditingCertificate, setIsEditingCertificate] = useState(false);

  // API data state
  const [appointmentData, setAppointmentData] = useState(null);
  const [patientData, setPatientData] = useState(null);
  const [loading, setLoading] = useState(true);

  // Derive status from appointment data
  const status =
    appointmentData?.appointmentStatus?.toLowerCase() ||
    APPOINTMENT_STATUS.UPCOMING;
  const isCompleted = status === APPOINTMENT_STATUS.COMPLETED;

  // Helper functions for button visibility logic
  const isMoreThan48Hours = (startDateTime) => {
    const appointmentStart = new Date(startDateTime);
    const now = new Date();
    const diffMs = appointmentStart.getTime() - now.getTime();
    const diffHours = diffMs / (1000 * 60 * 60);
    return diffHours > 48;
  };

  const isAppointmentActive = (startDateTime, endDateTime) => {
    const now = new Date();
    const start = new Date(startDateTime);
    const end = new Date(endDateTime);
    return now >= start && now <= end;
  };

  // Determine button visibility based on time conditions
  const shouldShowCancelButton =
    appointmentData?.startDateTime &&
    appointmentData?.appointmentStatus === 'upcoming'
      ? isMoreThan48Hours(appointmentData.startDateTime)
      : false;

  const shouldShowJoinButton =
    appointmentData?.startDateTime &&
    appointmentData?.endDateTime &&
    appointmentData?.appointmentStatus === 'upcoming'
      ? isAppointmentActive(
          appointmentData.startDateTime,
          appointmentData.endDateTime,
        )
      : false;

  const handleViewProfile = () => {
    const patientId = appointmentData?.patient?.id;
    if (patientId) {
      navigate("/doctor/patient-profile", {
        state: { patientId },
      });
    } else {
      toast({
        title: "Error",
        description: "Patient ID not found",
        variant: "destructive",
      });
    }
  };

  const handleCall = () => {
    navigate("/doctor/doctor-consultation-panel");
  };

  const handleToggleMobileMenu = () => {
    setMobileMenuOpen((prev) => !prev);
  };

  const handleToggleEditingSOAP = () => {
    setIsEditingSOAP((prev) => !prev);
  };

  const handleToggleEditingLabs = () => {
    setIsEditingLabs((prev) => !prev);
  };

  const handleToggleEditingROS = () => {
    setIsEditingROS((prev) => !prev);
  };

  const handleToggleEditingImaging = () => {
    setIsEditingImaging((prev) => !prev);
  };

  const handleToggleEditingCertificate = () => {
    setIsEditingCertificate((prev) => !prev);
  };

  const fetchAppointmentDetails = useCallback(async () => {
    if (!appointmentId) {
      toast({
        title: "Error",
        description: "No appointment ID provided",
        variant: "destructive",
      });
      setLoading(false);
      return;
    }
    setLoading(true);
    try {
      const response = await getAppointmentById_apiCalls({ id: appointmentId });
      if (response?.data) {
        setAppointmentData(response.data);
        if (response.data.patient) {
          const patient = response.data.patient;
          // setPatientData({
          //   name: `${patient.firstName || ""} ${patient.lastName || ""}`.trim(),
          //   gender: patient.gender || "Not specified",
          //   dob: patient.dob ?? "Not specified",
          //   phone: patient.contactNumber || "Not provided",
          //   email: patient.email || "Not provided",
          //   address: patient.address || "Not provided",
          //   country: patient.country || "Not specified",
          //   avatar: patient.profileImage || DefaultAvatar,
          //   medicalConcerns:
          //     response.data.consultation?.serviceName || "Not specified",
          // });
          setPatientData(patient);
        }
      }
    } catch (error) {
      console.error("Failed to fetch appointment details:", error);
      toast({
        title: "Error",
        description: "Failed to fetch appointment details. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  }, [appointmentId, toast]);

  useEffect(() => {
    fetchAppointmentDetails();
  }, [fetchAppointmentDetails]);

  const renderHeader = useCallback(
    () => (
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 mb-6">
        <div>
          <div className="flex items-center gap-2 mb-2">
            <h1 className="text-2xl font-bold">Appointment Details</h1>
            <Badge
              className={
                status === APPOINTMENT_STATUS.COMPLETED
                  ? "bg-blue-100 text-blue-600 badge capitalize hover:bg-blue-100"
                  : status === APPOINTMENT_STATUS.CANCELED
                    ? "bg-red-100 text-red-600 badge capitalize hover:bg-red-100"
                    : "bg-yellow-100 text-yellow-600 badge capitalize hover:bg-yellow-100"
              }
            >
              {status}
            </Badge>
          </div>
          <p className="text-gray-600">
            {appointmentData?.startDateTime
              ? new Date(appointmentData.startDateTime).toLocaleString(
                  "en-US",
                  {
                    year: "numeric",
                    month: "short",
                    day: "numeric",
                    hour: "2-digit",
                    minute: "2-digit",
                    hour12: true,
                  },
                )
              : "Date not available"}
            <span className="text-gray-500">
              {" "}
              ({appointmentData?.duration || 15} minutes)
            </span>
          </p>
        </div>
        {status === APPOINTMENT_STATUS.COMPLETED && (
          <div className="flex items-center flex-row gap-2"></div>
        )}
        {status === APPOINTMENT_STATUS.UPCOMING && (
          <div className="flex gap-2 w-full sm:w-auto">
            <Button
              variant="destructive"
              className="flex-1 sm:flex-none text-[#ffffff]"
              size={"lg"}
            >
              Cancel Appointment
            </Button>
            <Button variant={"primary"} size={"lg"} onClick={handleCall}>
              Join with Twilio
            </Button>
          </div>
        )}
        {status === APPOINTMENT_STATUS.CANCELED && (
          <div className="flex gap-2 w-full sm:w-auto"></div>
        )}
      </div>
    ),
    [status, appointmentData, handleCall],
  );

  const renderCanceledView = useCallback(
    () => (
      <Card>
        <CardContent className="flex flex-col items-center justify-center py-16 text-center">
          <div className="w-72 h-72 mb-6 bg-gray-100 rounded-lg flex items-center justify-center">
            <img src={CancelledAppointmentIllistration} alt="No appointments" />
          </div>
          <h3 className="text-lg font-semibold mb-2">Appointment Canceled</h3>
          <p className="text-gray-600">This appointment has been canceled.</p>
        </CardContent>
      </Card>
    ),
    [],
  );

  // Simple tabs component
  const renderTabs = () => (
    <div className="space-y-4">
      {/* Mobile menu button */}
      <div className="lg:hidden">
        <Button
          variant="outline"
          onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
          className="w-full justify-between"
        >
          <span>Menu</span>
          <Menu className="h-4 w-4" />
        </Button>
      </div>

      <Tabs defaultValue="pre-questioner" className="w-full">
        <div className="flex flex-col lg:flex-row gap-6">
          {/* Sidebar - hidden on mobile unless menu is open */}
          <div
            className={`lg:block lg:w-64 ${mobileMenuOpen ? "block" : "hidden"}`}
          >
            <Card>
              <CardContent className="p-4">
                <TabsList className="flex flex-col h-auto w-full space-y-1 bg-transparent">
                  <TabsTrigger
                    value="pre-questioner"
                    className="w-full justify-start"
                  >
                    Pre Questioner
                  </TabsTrigger>
                  <TabsTrigger
                    value="patient-soap"
                    className="w-full justify-start"
                  >
                    Patient SOAP
                  </TabsTrigger>
                  <TabsTrigger value="labs" className="w-full justify-start">
                    Labs
                  </TabsTrigger>
                  <TabsTrigger
                    value="prescription"
                    className="w-full justify-start"
                  >
                    Prescription
                  </TabsTrigger>
                  <TabsTrigger value="ros" className="w-full justify-start">
                    ROS
                  </TabsTrigger>
                  <TabsTrigger
                    value="compliance"
                    className="w-full justify-start"
                  >
                    Patient Compliance
                  </TabsTrigger>
                  <TabsTrigger value="imaging" className="w-full justify-start">
                    Imaging
                  </TabsTrigger>
                  <TabsTrigger
                    value="certificate"
                    className="w-full justify-start"
                  >
                    Medical Certificate
                  </TabsTrigger>
                  <TabsTrigger
                    value="activity"
                    className="w-full justify-start"
                  >
                    Activity
                  </TabsTrigger>
                </TabsList>
              </CardContent>
            </Card>
          </div>

          {/* Content area */}
          <div className="flex-1">
            <div>
              <div className="">
                <TabsContent value="pre-questioner">
                  <div className="flex items-center justify-between flex-row w-full">
                    <h5 className="font-[700] text-[24px]">
                      Pre-Questioner Details
                    </h5>

                    {user?.userType === "patient" &&
                    appointmentData?.appointmentStatus === "Upcoming" ? (
                      <Button
                        variant="outline"
                        size="sm"
                        // onClick={() => navigate("/doctor/forgot-password/email")}
                        className="flex items-center gap-2"
                      >
                        <Edit className="h-4 w-4" />
                        Edit
                      </Button>
                    ) : null}
                  </div>

                  <div className="mt-4">
                    <PreQuestioner />
                  </div>
                </TabsContent>

                <TabsContent value="patient-soap">
                  <div>
                    <div className="flex items-center justify-between w-full mb-4">
                      <h2 className="font-[700] text-[24px] ">SOAP</h2>
                      {!isEditingSOAP && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setIsEditingSOAP(true)}
                          className="flex items-center gap-2"
                        >
                          <Edit className="h-4 w-4" />
                          Edit
                        </Button>
                      )}
                      {isEditingSOAP && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setIsEditingSOAP(false)}
                          className="flex items-center gap-2"
                        >
                          Back to View
                        </Button>
                      )}
                    </div>
                    <div>
                      {!isEditingSOAP ? (
                        <PatientSOAPFilled appointmentId={appointmentId} />
                      ) : (
                        <PatientSOAP appointmentId={appointmentId} />
                      )}
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="labs">
                  <div>
                    <div className="flex items-center justify-between w-full mb-4">
                      <h2 className="font-[700] text-[24px] ">
                        Laboratory test request form
                      </h2>
                      {!isEditingLabs && (
                        <div className="flex justify-end mb-4">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setIsEditingLabs(true)}
                            className="flex items-center gap-2"
                          >
                            <Edit className="h-4 w-4" />
                            Edit
                          </Button>
                        </div>
                      )}
                      {isEditingLabs && (
                        <div className="flex justify-end mb-4">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setIsEditingLabs(false)}
                            className="flex items-center gap-2"
                          >
                            Back to View
                          </Button>
                        </div>
                      )}
                    </div>
                    {!isEditingLabs ? (
                      <LabsFilled appointmentId={appointmentId} />
                    ) : (
                      <Labs appointmentId={appointmentId} />
                    )}
                  </div>
                </TabsContent>

                <TabsContent value="prescription">
                  <Prescription />
                </TabsContent>

                <TabsContent value="ros">
                  <div>
                    <div className="flex items-center justify-between w-full mb-4">
                      <h2 className="font-[700] text-[24px] ">ROS</h2>
                      {!isEditingROS && (
                        <div className="flex justify-end mb-4">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setIsEditingROS(true)}
                            className="flex items-center gap-2"
                          >
                            <Edit className="h-4 w-4" />
                            Edit
                          </Button>
                        </div>
                      )}
                      {isEditingROS && (
                        <div className="flex justify-end mb-4">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setIsEditingROS(false)}
                            className="flex items-center gap-2"
                          >
                            Back to View
                          </Button>
                        </div>
                      )}
                    </div>
                    {!isEditingROS ? <ROSFilled appointmentId={appointmentData?.id} /> : <ROS appointmentId={appointmentData?.id} />}
                  </div>
                </TabsContent>

                <TabsContent value="compliance">
                  {isCompleted ? <PatientCompliance /> : <PatientCompliance />}
                </TabsContent>

                <TabsContent value="imaging">
                  <div>
                    <div className="flex items-center justify-between w-full mb-4">
                      <h2 className="font-[700] text-[24px] ">Imaging</h2>
                      {!isEditingImaging && (
                        <div className="flex justify-end mb-4">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setIsEditingImaging(true)}
                            className="flex items-center gap-2"
                          >
                            <Edit className="h-4 w-4" />
                            Edit
                          </Button>
                        </div>
                      )}
                      {isEditingImaging && (
                        <div className="flex justify-end mb-4">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setIsEditingImaging(false)}
                            className="flex items-center gap-2"
                          >
                            Back to View
                          </Button>
                        </div>
                      )}
                    </div>
                    {!isEditingImaging ? <ImagingFilled /> : <Imaging />}
                  </div>
                </TabsContent>

                <TabsContent value="certificate">
                  <div>
                    <div className="flex items-center justify-between w-full mb-4">
                      <h2 className="font-[700] text-[24px] ">
                        Medical Certificate
                      </h2>
                      {!isEditingCertificate && (
                        <div className="flex justify-end mb-4">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setIsEditingCertificate(true)}
                            className="flex items-center gap-2"
                          >
                            <Edit className="h-4 w-4" />
                            Edit
                          </Button>
                        </div>
                      )}
                      {isEditingCertificate && (
                        <div className="flex justify-end mb-4">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setIsEditingCertificate(false)}
                            className="flex items-center gap-2"
                          >
                            Back to View
                          </Button>
                        </div>
                      )}
                    </div>
                    {!isEditingCertificate ? (
                      <MedicalCertificateFilled />
                    ) : (
                      <MedicalCertificate />
                    )}
                  </div>
                </TabsContent>

                <TabsContent value="activity">
                  <Activity />
                </TabsContent>
              </div>
            </div>
          </div>
        </div>
      </Tabs>
    </div>
  );

  // Main render logic
  if (loading) {
    return (
      <div className="mx-auto px-4 py-6 flex justify-center items-center min-h-[400px]">
        <div className="flex flex-col items-center gap-4">
          <Loader2 className="h-8 w-8 animate-spin" />
          <p className="text-gray-600">Loading appointment details...</p>
        </div>
      </div>
    );
  }

  if (!appointmentData) {
    return (
      <div className="mx-auto px-4 py-6 flex justify-center items-center min-h-[400px]">
        <div className="flex flex-col items-center gap-4">
          <p className="text-gray-600">Appointment not found</p>
          <Button onClick={() => navigate(-1)} variant="outline">
            Go Back
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="mx-auto px-4 py-6">
      {renderHeader()}
      <PatientProfile
        patientData={patientData}
        appointmentData={appointmentData}
        handleViewProfile={handleViewProfile}
      />
      {status === APPOINTMENT_STATUS.CANCELED
        ? renderCanceledView()
        : renderTabs()}
    </div>
  );
};

export default AppointmentsDetails;
