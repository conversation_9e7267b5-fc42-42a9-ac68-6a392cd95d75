// Field configurations and schema for ROS form sections based on complete schema
// This file contains all field definitions and Zod schema used by both ROS.jsx and ROSFilled.jsx

import { z } from "zod";

// Zod schema for ROS form validation
export const rosFormSchema = z.object({
  appointmentId: z.string(),
  id: z.string().optional(),
  // GENERAL
  recentWeightChange: z.boolean().nullable().optional(),
  weakness: z.boolean().nullable().optional(),
  fatigue: z.boolean().nullable().optional(),
  fever: z.boolean().nullable().optional(),

  // SKIN
  skinRashes: z.boolean().nullable().optional(),
  skinLumps: z.boolean().nullable().optional(),
  skinSores: z.boolean().nullable().optional(),
  skinItching: z.boolean().nullable().optional(),
  skinDryness: z.boolean().nullable().optional(),
  skinChangeInColor: z.boolean().nullable().optional(),
  skinChangeInHair: z.boolean().nullable().optional(),
  skinChangeInMoles: z.boolean().nullable().optional(),

  // HEENT - Head
  headache: z.boolean().nullable().optional(),
  headInjury: z.boolean().nullable().optional(),
  dizziness: z.boolean().nullable().optional(),
  lightheadedness: z.boolean().nullable().optional(),

  // HEENT - Eyes
  visionChange: z.boolean().nullable().optional(),
  glassesOrContactLenses: z.boolean().nullable().optional(),
  eyePain: z.boolean().nullable().optional(),
  eyeRedness: z.boolean().nullable().optional(),
  excessiveTearing: z.boolean().nullable().optional(),
  doubleOrBlurredVision: z.boolean().nullable().optional(),
  spots: z.boolean().nullable().optional(),
  specks: z.boolean().nullable().optional(),
  flashingLights: z.boolean().nullable().optional(),
  glaucoma: z.boolean().nullable().optional(),
  cataracts: z.boolean().nullable().optional(),

  // HEENT - Ears
  hearingLoss: z.boolean().nullable().optional(),
  tinnitus: z.boolean().nullable().optional(),
  vertigo: z.boolean().nullable().optional(),
  earaches: z.boolean().nullable().optional(),
  earInfection: z.boolean().nullable().optional(),
  earDischarge: z.boolean().nullable().optional(),
  hearingDecreased: z.boolean().nullable().optional(),
  hearingAidsUse: z.boolean().nullable().optional(),

  // HEENT - Nose and Sinuses
  nasalDischarge: z.boolean().nullable().optional(),
  nasalItching: z.boolean().nullable().optional(),
  frequentColds: z.boolean().nullable().optional(),
  hayfever: z.boolean().nullable().optional(),
  nasalStuffiness: z.boolean().nullable().optional(),
  nosebleeds: z.boolean().nullable().optional(),
  sinusPressurePain: z.boolean().nullable().optional(),

  // HEENT - Throat
  dentalCondition: z.boolean().nullable().optional(),
  gumProblemsBleeding: z.boolean().nullable().optional(),
  dentures: z.boolean().nullable().optional(),
  denturesFit: z.string().nullable().optional(),
  lastDentalExam: z.date().nullable().optional(),
  soreTongue: z.boolean().nullable().optional(),
  dryMouth: z.boolean().nullable().optional(),
  frequentSoreThroats: z.boolean().nullable().optional(),
  hoarseness: z.boolean().nullable().optional(),

  // NECK
  swollenGlands: z.boolean().nullable().optional(),
  thyroidProblems: z.boolean().nullable().optional(),
  goiter: z.boolean().nullable().optional(),
  neckLumps: z.boolean().nullable().optional(),
  neckPainStiffness: z.boolean().nullable().optional(),

  // BREASTS
  breastLumps: z.boolean().nullable().optional(),
  breastPainDiscomfort: z.boolean().nullable().optional(),
  nippleDischarge: z.boolean().nullable().optional(),
  selfExamPractices: z.boolean().nullable().optional(),

  // RESPIRATORY
  cough: z.boolean().nullable().optional(),
  sputum: z.boolean().nullable().optional(),
  sputumColor: z.string().nullable().optional(),
  sputumQuantity: z.string().nullable().optional(),
  sputumBlood: z.boolean().nullable().optional(),
  shortnessOfBreath_respiratory: z.boolean().nullable().optional(),
  wheezing: z.boolean().nullable().optional(),
  pleuriticPain: z.boolean().nullable().optional(),
  lastChestXray: z.date().nullable().optional(),
  asthma: z.boolean().nullable().optional(),
  bronchitis: z.boolean().nullable().optional(),
  emphysema: z.boolean().nullable().optional(),
  pneumonia: z.boolean().nullable().optional(),
  tuberculosis: z.boolean().nullable().optional(),

  // CARDIOVASCULAR
  heartTrouble: z.boolean().nullable().optional(),
  highBloodPressure: z.boolean().nullable().optional(),
  rheumaticFever: z.boolean().nullable().optional(),
  heartMurmurs: z.boolean().nullable().optional(),
  chestPain: z.boolean().nullable().optional(),
  palpitations: z.boolean().nullable().optional(),
  shortnessOfBreath_cardio: z.boolean().nullable().optional(),
  orthopnea: z.boolean().nullable().optional(),
  paroxysmalNocturnalDyspnea: z.boolean().nullable().optional(),
  edema: z.boolean().nullable().optional(),
  ekgOther: z.string().nullable().optional(),

  // GASTROINTESTINAL
  unintentionalWeightChange: z.boolean().nullable().optional(),
  troubleSwallowing: z.boolean().nullable().optional(),
  heartburn: z.boolean().nullable().optional(),
  appetite: z.boolean().nullable().optional(),
  specialDiet: z.boolean().nullable().optional(),
  nausea: z.boolean().nullable().optional(),
  stoolColor: z.string().nullable().optional(),
  stoolSize: z.string().nullable().optional(),
  changeInBowelHabits: z.boolean().nullable().optional(),
  painWithDefecation: z.boolean().nullable().optional(),
  rectalBleeding: z.boolean().nullable().optional(),
  blackOrTarryStools: z.boolean().nullable().optional(),
  hemorrhoids: z.boolean().nullable().optional(),
  constipation: z.boolean().nullable().optional(),
  diarrhea: z.boolean().nullable().optional(),
  abdominalPain: z.boolean().nullable().optional(),
  foodIntolerance: z.boolean().nullable().optional(),
  excessiveBelching: z.boolean().nullable().optional(),
  jaundice: z.boolean().nullable().optional(),
  liverGallbladderTrouble: z.boolean().nullable().optional(),
  hepatitis: z.boolean().nullable().optional(),

  // PERIPHERAL VASCULAR
  claudication: z.boolean().nullable().optional(),
  legCramps: z.boolean().nullable().optional(),
  varicoseVeins: z.boolean().nullable().optional(),
  pastClots: z.boolean().nullable().optional(),
  calfLegFeetSwelling: z.boolean().nullable().optional(),
  fingertipColorChangeCold: z.boolean().nullable().optional(),
  swellingRednessTendernessPeripheral: z.boolean().nullable().optional(),

  // URINARY
  frequencyOfUrination: z.boolean().nullable().optional(),
  polyuria: z.boolean().nullable().optional(),
  nocturia: z.boolean().nullable().optional(),
  urgency: z.boolean().nullable().optional(),
  burningPainDuringUrination: z.boolean().nullable().optional(),
  hematuria: z.boolean().nullable().optional(),
  urinaryInfections: z.boolean().nullable().optional(),
  kidneyOrFlankPain: z.boolean().nullable().optional(),
  kidneyStones: z.boolean().nullable().optional(),
  ureteralColic: z.boolean().nullable().optional(),
  suprapubicPain: z.boolean().nullable().optional(),
  incontinence: z.boolean().nullable().optional(),
  reducedUrinaryCaliberOrForce: z.boolean().nullable().optional(),
  hesitancy: z.boolean().nullable().optional(),
  dribbling: z.boolean().nullable().optional(),

  // GENITAL - MALE
  hernias: z.boolean().nullable().optional(),
  penileDischarge: z.boolean().nullable().optional(),
  testicularPainOrMasses: z.boolean().nullable().optional(),
  scrotalPainOrSwelling: z.boolean().nullable().optional(),

  // STD History
  stdHistory: z.boolean().nullable().optional(),
  stdTreatment: z.string().nullable().optional(),

  // Sexual Habits
  sexualInterest: z.boolean().nullable().optional(),
  sexualFunction: z.boolean().nullable().optional(),
  sexualSatisfaction: z.boolean().nullable().optional(),
  birthControlMethodUse: z.boolean().nullable().optional(),
  condomUse: z.boolean().nullable().optional(),
  sexualProblems: z.boolean().nullable().optional(),
  hivInfectionConcerns: z.boolean().nullable().optional(),

  // GENITAL - FEMALE
  ageAtMenarche: z.string().nullable().optional(),
  periodFrequency: z.string().nullable().optional(),
  durationOfPeriods: z.string().nullable().optional(),
  amountOfBleeding: z.string().nullable().optional(),
  periodRegularity: z.boolean().nullable().optional(),
  bleedingBetweenPeriods: z.boolean().nullable().optional(),
  bleedingAfterIntercourse: z.boolean().nullable().optional(),
  lastMenstrualPeriod: z.date().nullable().optional(),
  dysmenorrhea: z.boolean().nullable().optional(),
  premenstrualTension: z.boolean().nullable().optional(),
  ageAtMenopause: z.string().nullable().optional(),
  menopausalSymptoms: z.boolean().nullable().optional(),
  postMenopausalBleeding: z.boolean().nullable().optional(),
  vaginalDischarge: z.boolean().nullable().optional(),
  itching_female: z.boolean().nullable().optional(),
  sores_female: z.boolean().nullable().optional(),
  lumps_female: z.boolean().nullable().optional(),
  historyOfSexuallyTransmittedInfections_female: z
    .boolean()
    .nullable()
    .optional(),
  treatmentOfSexuallyTransmittedInfections_female: z
    .string()
    .nullable()
    .optional(),
  numberOfPregnancies: z.string().nullable().optional(),
  numberOfDeliveries: z.string().nullable().optional(),
  typeOfDeliveries: z.string().nullable().optional(),
  numberOfAbortions: z.string().nullable().optional(),
  birthControlMethods: z.string().nullable().optional(),
  complicationsOfPregnancy: z.boolean().nullable().optional(),

  // SEXUAL HABITS (FEMALE-SPECIFIC)
  sexualInterest_female: z.boolean().nullable().optional(),
  sexualFunction_female: z.boolean().nullable().optional(),
  sexualSatisfaction_female: z.boolean().nullable().optional(),
  anyProblemsIncludingDyspareunia_female: z.boolean().nullable().optional(),
  concernsAboutHivInfection_female: z.boolean().nullable().optional(),

  // MUSCULOSKELETAL
  muscleOrJointPain: z.boolean().nullable().optional(),
  stiffness: z.boolean().nullable().optional(),
  arthritis: z.boolean().nullable().optional(),
  gout: z.boolean().nullable().optional(),
  backache: z.boolean().nullable().optional(),
  muscleLocationDescription: z.string().nullable().optional(),
  swelling_muscle: z.boolean().nullable().optional(),
  redness_muscle: z.boolean().nullable().optional(),
  painMusculoskeletal: z.boolean().nullable().optional(),
  tenderness_muscle: z.boolean().nullable().optional(),
  weaknessMusculoskeletal: z.boolean().nullable().optional(),
  numbnessInLimb: z.boolean().nullable().optional(),
  limitationOfMotionOrActivity: z.boolean().nullable().optional(),
  timingMorning: z.boolean().nullable().optional(),
  timingEvening: z.boolean().nullable().optional(),
  duration: z.string().nullable().optional(),
  historyOfTrauma: z.boolean().nullable().optional(),
  neckOrLowBackPain: z.boolean().nullable().optional(),
  systemicJointPain: z.boolean().nullable().optional(),

  // PSYCHIATRIC
  nervousness: z.boolean().nullable().optional(),
  tension: z.boolean().nullable().optional(),
  feelingDownSadDepressed: z.boolean().nullable().optional(),
  memoryChange: z.boolean().nullable().optional(),
  suicidePlansOrAttempts: z.boolean().nullable().optional(),
  suicidalThoughts: z.boolean().nullable().optional(),
  pastCounseling: z.boolean().nullable().optional(),
  psychiatricAdmissions: z.boolean().nullable().optional(),
  onPsychiatricMedications: z.boolean().nullable().optional(),

  // NEUROLOGIC
  changeInMoodNeurologic: z.boolean().nullable().optional(),
  changeInAttention: z.boolean().nullable().optional(),
  changeInSpeech: z.boolean().nullable().optional(),
  changeInOrientation: z.boolean().nullable().optional(),
  changeInMemoryNeurologic: z.boolean().nullable().optional(),
  changeInInsight: z.boolean().nullable().optional(),
  changeInJudgement: z.boolean().nullable().optional(),
  headacheNeurologic: z.boolean().nullable().optional(),
  dizzinessNeurologic: z.boolean().nullable().optional(),
  vertigoNeurologic: z.boolean().nullable().optional(),
  fainting: z.boolean().nullable().optional(),
  blackouts: z.boolean().nullable().optional(),
  weaknessNeurologic: z.boolean().nullable().optional(),
  paralysis: z.boolean().nullable().optional(),
  numbnessOrLossOfSensation: z.boolean().nullable().optional(),
  tinglingPinsAndNeedles: z.boolean().nullable().optional(),
  tremorsOrOtherInvoluntaryMovement: z.boolean().nullable().optional(),
  seizures: z.boolean().nullable().optional(),

  // HEMATOLOGIC
  anemia: z.boolean().nullable().optional(),
  easyBruisingOrBleeding: z.boolean().nullable().optional(),
  pastTransfusions: z.boolean().nullable().optional(),
  transfusionReactions: z.boolean().nullable().optional(),

  // ENDOCRINE
  thyroidTrouble: z.boolean().nullable().optional(),
  heatOrColdIntolerance: z.boolean().nullable().optional(),
  excessiveSweating: z.boolean().nullable().optional(),
  excessiveThirstOrHunger: z.boolean().nullable().optional(),
  polyuriaEndocrine: z.boolean().nullable().optional(),
  changeInGloveOrShoeSize: z.boolean().nullable().optional(),
});

export const fieldConfigs = {
  general: {
    radio: [
      { name: "recentWeightChange", label: "Recent Weight Change" },
      { name: "weakness", label: "Weakness" },
      { name: "fatigue", label: "Fatigue" },
      { name: "fever", label: "Fever" },
    ]
  },
  skin: {
    radio: [
      { name: "skinRashes", label: "Rashes" },
      { name: "skinLumps", label: "Lumps" },
      { name: "skinSores", label: "Sores" },
      { name: "skinItching", label: "Itching" },
      { name: "skinDryness", label: "Dryness" },
      { name: "skinChangeInColor", label: "Change in Color" },
      { name: "skinChangeInHair", label: "Change in Hair" },
      { name: "skinChangeInMoles", label: "Change in Moles" },
    ]
  },
  heentHead: {
    radio: [
      { name: "headache", label: "Headache" },
      { name: "headInjury", label: "Head Injury" },
      { name: "dizziness", label: "Dizziness" },
      { name: "lightheadedness", label: "Lightheadedness" },
    ]
  },
  heentEyes: {
    radio: [
      { name: "visionChange", label: "Vision Change" },
      { name: "glassesOrContactLenses", label: "Glasses or Contact Lenses" },
      { name: "eyePain", label: "Eye Pain" },
      { name: "eyeRedness", label: "Eye Redness" },
      { name: "excessiveTearing", label: "Excessive Tearing" },
      { name: "doubleOrBlurredVision", label: "Double or Blurred Vision" },
      { name: "spots", label: "Spots" },
      { name: "specks", label: "Specks" },
      { name: "flashingLights", label: "Flashing Lights" },
      { name: "glaucoma", label: "Glaucoma" },
      { name: "cataracts", label: "Cataracts" },
    ]
  },
  heentEars: {
    radio: [
      { name: "hearingLoss", label: "Hearing Loss" },
      { name: "tinnitus", label: "Ringing in Ear(s)/Tinnitus" },
      { name: "vertigo", label: "Vertigo" },
      { name: "earaches", label: "Earaches" },
      { name: "earInfection", label: "Infection" },
      { name: "earDischarge", label: "Discharge" },
      { name: "hearingDecreased", label: "Hearing Decreased" },
      { name: "hearingAidsUse", label: "Use/non-use of hearing aids" },
    ]
  },
  heentNose: {
    radio: [
      { name: "nasalDischarge", label: "Discharge" },
      { name: "nasalItching", label: "Itching" },
      { name: "frequentColds", label: "Frequent Colds" },
      { name: "hayfever", label: "Hayfever" },
      { name: "nasalStuffiness", label: "Nasal Stuffiness" },
      { name: "nosebleeds", label: "Nosebleeds" },
      { name: "sinusPressurePain", label: "Sinus pressure/pain" },
    ]
  },
  heentThroat: {
    radio: [
      { name: "dentalCondition", label: "Condition of teeth and gum" },
      { name: "gumProblemsBleeding", label: "Gum Problems/ Bleeding" },
      { name: "dentures", label: "Dentures (if any)" },
      { name: "soreTongue", label: "Sore Tongue" },
      { name: "dryMouth", label: "Dry Mouth" },
      { name: "frequentSoreThroats", label: "Frequent Sore Throats" },
      { name: "hoarseness", label: "Hoarseness" },
    ],
    input: [
      { name: "denturesFit", label: "Fit of Dentures", placeholder: "Describe fit of dentures", type: "text" },
    ],
    date: [
      { name: "lastDentalExam", label: "Last Dental Exam", placeholder: "Enter date of last dental exam", type: "date" },
    ]
  },
  neck: {
    radio: [
      { name: "swollenGlands", label: "Swollen Glands" },
      { name: "thyroidProblems", label: "Thyroid Problems" },
      { name: "goiter", label: "Goiter" },
      { name: "neckLumps", label: "Lumps" },
      { name: "neckPainStiffness", label: "Pain or Stiffness" },
    ]
  },
  breasts: {
    radio: [
      { name: "breastLumps", label: "Lumps" },
      { name: "breastPainDiscomfort", label: "Pain or discomfort" },
      { name: "nippleDischarge", label: "Nipple discharge" },
      { name: "selfExamPractices", label: "Self-exam practices" },
    ]
  },
  respiratory: {
    radio: [
      { name: "cough", label: "Cough" },
      { name: "sputum", label: "Sputum" },
      { name: "sputumBlood", label: "Presence of blood" },
      { name: "shortnessOfBreath_respiratory", label: "Shortness of breath (Dyspnea)" },
      { name: "wheezing", label: "Wheezing" },
      { name: "pleuriticPain", label: "Pain with a deep breath (Pleuritic pain)" },
      { name: "asthma", label: "Asthma" },
      { name: "bronchitis", label: "Bronchitis" },
      { name: "emphysema", label: "Emphysema" },
      { name: "pneumonia", label: "Pneumonia" },
      { name: "tuberculosis", label: "Tuberculosis" },
    ],
    input: [
      { name: "sputumColor", label: "Sputum Color", placeholder: "Describe sputum color", type: "text" },
      { name: "sputumQuantity", label: "Sputum Quantity", placeholder: "Describe sputum quantity", type: "text" },
    ],
    date: [
      { name: "lastChestXray", label: "Last Chest X-Ray", placeholder: "Enter date of last chest X-ray", type: "date" },
    ]
  },
  cardiovascular: {
    radio: [
      { name: "heartTrouble", label: "Heart Trouble" },
      { name: "highBloodPressure", label: "High Blood Pressure" },
      { name: "rheumaticFever", label: "Rheumatic Fever" },
      { name: "heartMurmurs", label: "Heart Murmurs" },
      { name: "chestPain", label: "Chest pain or discomfort" },
      { name: "palpitations", label: "Palpitations" },
      { name: "shortnessOfBreath_cardio", label: "Shortness of Breath" },
      { name: "orthopnea", label: "Use of pillows or head of bed elevation at night to ease breathing (Orthopnea)" },
      { name: "paroxysmalNocturnalDyspnea", label: "Need to sit up at night to ease breathing (paroxysmal nocturnal dyspnea)" },
      { name: "edema", label: "Swelling in the hands, ankles or feet (edema)" },
    ],
    input: [
      { name: "ekgOther", label: "Results of past EKG or other Cardiovascular tests EKG/ Other:", placeholder: "Value", type: "text" },
    ]
  },
  gastrointestinal: {
    radio: [
      { name: "unintentionalWeightChange", label: "Unintentional Weight loss/ gain" },
      { name: "troubleSwallowing", label: "Trouble swallowing" },
      { name: "heartburn", label: "Heartburn" },
      { name: "appetite", label: "Appetite" },
      { name: "specialDiet", label: "On Special Diet" },
      { name: "nausea", label: "Nausea" },
      { name: "changeInBowelHabits", label: "Change in Bowel Habits" },
      { name: "painWithDefecation", label: "Pain with Defecation" },
      { name: "rectalBleeding", label: "Rectal Bleeding" },
      { name: "blackOrTarryStools", label: "Black or Tarry Stools" },
      { name: "hemorrhoids", label: "Hemorrhoids" },
      { name: "constipation", label: "Constipation" },
      { name: "diarrhea", label: "Diarrhea/Irritable Bowel Syndrome" },
      { name: "abdominalPain", label: "Abdominal Pain" },
      { name: "foodIntolerance", label: "Food Intolerance" },
      { name: "excessiveBelching", label: "Excessive belching or passing of gas" },
      { name: "jaundice", label: "Jaundice (yellowing of skin)" },
      { name: "liverGallbladderTrouble", label: "Liver or Gallbladder Trouble" },
      { name: "hepatitis", label: "Hepatitis" },
    ],
    input: [
      { name: "stoolColor", label: "Stool Color", placeholder: "Describe stool color", type: "text" },
      { name: "stoolSize", label: "Stool Size", placeholder: "Describe stool size", type: "text" },
    ]
  },
  peripheralVascular: {
    radio: [
      { name: "claudication", label: "Intermittent leg pain with exertion (claudication)" },
      { name: "legCramps", label: "Leg Cramps" },
      { name: "varicoseVeins", label: "Varicose Veins" },
      { name: "pastClots", label: "Past clots in the veins" },
      { name: "calfLegFeetSwelling", label: "Swelling in the calves, legs, or feet" },
      { name: "fingertipColorChangeCold", label: "Color change in fingertips/toes during cold weather" },
      { name: "swellingRednessTendernessPeripheral", label: "Swelling with redness/tenderness" },
    ]
  },
  urinary: {
    radio: [
      { name: "frequencyOfUrination", label: "Frequency of Urination" },
      { name: "polyuria", label: "Polyuria" },
      { name: "nocturia", label: "Nocturia" },
      { name: "urgency", label: "Urgency" },
      { name: "burningPainDuringUrination", label: "Burning or Pain During Urination" },
      { name: "hematuria", label: "Hematuria" },
      { name: "urinaryInfections", label: "Urinary infections" },
      { name: "kidneyOrFlankPain", label: "Kidney or Flank Pain" },
      { name: "kidneyStones", label: "Kidney Stones" },
      { name: "ureteralColic", label: "Ureteral Colic" },
      { name: "suprapubicPain", label: "Suprapubic Pain" },
      { name: "incontinence", label: "Incontinence" },
      { name: "reducedUrinaryCaliberOrForce", label: "Reduced caliber or force of the urinary system" },
      { name: "hesitancy", label: "Hesitancy" },
      { name: "dribbling", label: "Dribbling" },
    ]
  },
  genitalMale: {
    radio: [
      { name: "hernias", label: "Hernias" },
      { name: "penileDischarge", label: "Discharge from sores on the penis" },
      { name: "testicularPainOrMasses", label: "Testicular Pain or Masses" },
      { name: "scrotalPainOrSwelling", label: "Scrotal Pain or Swelling" },
    ]
  },
  stdHistory: {
    radio: [
      { name: "stdHistory", label: "History of Sexually Transmitted infections" },
    ],
    input: [
      { name: "stdTreatment", label: "Treatment", placeholder: "Describe STD treatment", type: "text" },
    ]
  },
  sexualHabits: {
    radio: [
      { name: "sexualInterest", label: "Interest" },
      { name: "sexualFunction", label: "Function" },
      { name: "sexualSatisfaction", label: "Satisfaction" },
      { name: "birthControlMethodUse", label: "Birth Control Methods" },
      { name: "condomUse", label: "Condom Use" },
      { name: "sexualProblems", label: "Problems" },
      { name: "hivInfectionConcerns", label: "Concerns about HIV Infection" },
    ]
  },
  genitalFemale: {
    radio: [
      { name: "periodRegularity", label: "Period Regularity" },
      { name: "bleedingBetweenPeriods", label: "Bleeding Between Periods" },
      { name: "bleedingAfterIntercourse", label: "Bleeding After Intercourse" },
      { name: "dysmenorrhea", label: "Dysmenorrhea" },
      { name: "premenstrualTension", label: "Premenstrual Tension" },
      { name: "menopausalSymptoms", label: "Menopausal Symptoms" },
      { name: "postMenopausalBleeding", label: "Post-Menopausal Bleeding" },
      { name: "vaginalDischarge", label: "Vaginal Discharge" },
      { name: "itching_female", label: "Itching" },
      { name: "sores_female", label: "Sores" },
      { name: "lumps_female", label: "Lumps" },
      { name: "historyOfSexuallyTransmittedInfections_female", label: "History of Sexually Transmitted infections" },
      { name: "complicationsOfPregnancy", label: "Complications of pregnancy" },
    ],
    input: [
      { name: "ageAtMenarche", label: "Age at Menarche", placeholder: "Enter age", type: "text" },
      { name: "periodFrequency", label: "Period Frequency", placeholder: "Enter frequency", type: "text" },
      { name: "durationOfPeriods", label: "Duration of Periods", placeholder: "Enter duration", type: "text" },
      { name: "amountOfBleeding", label: "Amount of Bleeding", placeholder: "Describe amount", type: "text" },
      { name: "ageAtMenopause", label: "Age at Menopause", placeholder: "Enter age", type: "text" },
      { name: "treatmentOfSexuallyTransmittedInfections_female", label: "Treatment", placeholder: "value", type: "text" },
      { name: "numberOfPregnancies", label: "Number of pregnancies", placeholder: "value", type: "text" },
      { name: "numberOfDeliveries", label: "Number of deliveries", placeholder: "value", type: "text" },
      { name: "typeOfDeliveries", label: "Type of deliveries", placeholder: "value", type: "text" },
      { name: "numberOfAbortions", label: "Number of Abortions", placeholder: "value", type: "text" },
      { name: "birthControlMethods", label: "Birth Control Methods", placeholder: "value", type: "text" },
    ],
    date: [
      { name: "lastMenstrualPeriod", label: "Last Menstrual Period", placeholder: "Enter date", type: "date" },
    ]
  },
  sexualHabitsFemale: {
    radio: [
      { name: "sexualInterest_female", label: "Interest" },
      { name: "sexualFunction_female", label: "Function" },
      { name: "sexualSatisfaction_female", label: "Satisfaction" },
      { name: "anyProblemsIncludingDyspareunia_female", label: "Any problems including dyspareunia" },
      { name: "concernsAboutHivInfection_female", label: "Concerns about HIV infection" },
    ]
  },
  musculoskeletal: {
    radio: [
      { name: "muscleOrJointPain", label: "Muscle or joint pain" },
      { name: "stiffness", label: "Stiffness" },
      { name: "arthritis", label: "Arthritis" },
      { name: "gout", label: "Gout" },
      { name: "backache", label: "Backache" },
      { name: "swelling_muscle", label: "Swelling" },
      { name: "redness_muscle", label: "Redness" },
      { name: "painMusculoskeletal", label: "Pain" },
      { name: "tenderness_muscle", label: "Tenderness" },
      { name: "weaknessMusculoskeletal", label: "Weakness" },
      { name: "numbnessInLimb", label: "Numbness in Limb" },
      { name: "limitationOfMotionOrActivity", label: "Limitation of Motion or Activity" },
      { name: "timingMorning", label: "Morning" },
      { name: "timingEvening", label: "Evening" },
      { name: "historyOfTrauma", label: "History of Trauma" },
      { name: "neckOrLowBackPain", label: "Neck or Low Back Pain" },
      { name: "systemicJointPain", label: "Joint pain with systemic symptoms such as fever, chills, rash, anorexia, weight loss or weakness" },
    ],
    input: [
      { name: "muscleLocationDescription", label: "If present, describe location of affected joints/muscles:", placeholder: "Describe affected areas", type: "text" },
      { name: "duration", label: "Duration", placeholder: "Enter duration", type: "text" },
    ]
  },
  psychiatric: {
    radio: [
      { name: "nervousness", label: "Nervousness" },
      { name: "tension", label: "Tension" },
      { name: "feelingDownSadDepressed", label: "Feeling Down, Sad, or Depressed" },
      { name: "memoryChange", label: "Memory Change" },
      { name: "suicidePlansOrAttempts", label: "Suicide Plans or Attempts" },
      { name: "suicidalThoughts", label: "Suicidal Thoughts" },
      { name: "pastCounseling", label: "Past Counseling" },
      { name: "psychiatricAdmissions", label: "Psychiatric Admissions" },
      { name: "onPsychiatricMedications", label: "On Psychiatric Medications" },
    ]
  },
  neurologic: {
    radio: [
      { name: "changeInMoodNeurologic", label: "Change in Mood" },
      { name: "changeInAttention", label: "Change in Attention" },
      { name: "changeInSpeech", label: "Change in Speech" },
      { name: "changeInOrientation", label: "Change in Orientation" },
      { name: "changeInMemoryNeurologic", label: "Change in Memory" },
      { name: "changeInInsight", label: "Change in Insight" },
      { name: "changeInJudgement", label: "Change in Judgement" },
      { name: "headacheNeurologic", label: "Headache" },
      { name: "dizzinessNeurologic", label: "Dizziness" },
      { name: "vertigoNeurologic", label: "Vertigo" },
      { name: "fainting", label: "Fainting" },
      { name: "blackouts", label: "Blackouts" },
      { name: "weaknessNeurologic", label: "Weakness" },
      { name: "paralysis", label: "Paralysis" },
      { name: "numbnessOrLossOfSensation", label: "Numbness or Loss of Sensation" },
      { name: "tinglingPinsAndNeedles", label: "Tingling or Pins and Needles" },
      { name: "tremorsOrOtherInvoluntaryMovement", label: "Tremors or Other involuntary movement" },
      { name: "seizures", label: "Seizures" },
    ]
  },
  hematologic: {
    radio: [
      { name: "anemia", label: "Anemia" },
      { name: "easyBruisingOrBleeding", label: "Easy bruising or bleeding" },
      { name: "pastTransfusions", label: "Past transfusions" },
      { name: "transfusionReactions", label: "Transfusion reactions" },
    ]
  },
  endocrine: {
    radio: [
      { name: "thyroidTrouble", label: "Thyroid trouble" },
      { name: "heatOrColdIntolerance", label: "Heat or Cold Intolerance" },
      { name: "excessiveSweating", label: "Excessive Sweating" },
      { name: "excessiveThirstOrHunger", label: "Excessive thirst or hunger" },
      { name: "polyuriaEndocrine", label: "Polyuria" },
      { name: "changeInGloveOrShoeSize", label: "Change in glove or shoe size" },
    ]
  },
};
