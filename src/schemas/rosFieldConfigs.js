// Field configurations for ROS form sections based on complete schema
// This file contains all field definitions used by both ROS.jsx and ROSFilled.jsx

export const fieldConfigs = {
  general: {
    radio: [
      { name: "recentWeightChange", label: "Recent Weight Change" },
      { name: "weakness", label: "Weakness" },
      { name: "fatigue", label: "Fatigue" },
      { name: "fever", label: "Fever" },
    ]
  },
  skin: {
    radio: [
      { name: "skinRashes", label: "Rashes" },
      { name: "skinLumps", label: "Lumps" },
      { name: "skinSores", label: "Sores" },
      { name: "skinItching", label: "Itching" },
      { name: "skinDryness", label: "Dryness" },
      { name: "skinChangeInColor", label: "Change in Color" },
      { name: "skinChangeInHair", label: "Change in Hair" },
      { name: "skinChangeInMoles", label: "Change in Moles" },
    ]
  },
  heentHead: {
    radio: [
      { name: "headache", label: "Headache" },
      { name: "headInjury", label: "Head Injury" },
      { name: "dizziness", label: "Dizziness" },
      { name: "lightheadedness", label: "Lightheadedness" },
    ]
  },
  heentEyes: {
    radio: [
      { name: "visionChange", label: "Vision Change" },
      { name: "glassesOrContactLenses", label: "Glasses or Contact Lenses" },
      { name: "eyePain", label: "Eye Pain" },
      { name: "eyeRedness", label: "Eye Redness" },
      { name: "excessiveTearing", label: "Excessive Tearing" },
      { name: "doubleOrBlurredVision", label: "Double or Blurred Vision" },
      { name: "spots", label: "Spots" },
      { name: "specks", label: "Specks" },
      { name: "flashingLights", label: "Flashing Lights" },
      { name: "glaucoma", label: "Glaucoma" },
      { name: "cataracts", label: "Cataracts" },
    ]
  },
  heentEars: {
    radio: [
      { name: "hearingLoss", label: "Hearing Loss" },
      { name: "tinnitus", label: "Ringing in Ear(s)/Tinnitus" },
      { name: "vertigo", label: "Vertigo" },
      { name: "earaches", label: "Earaches" },
      { name: "earInfection", label: "Infection" },
      { name: "earDischarge", label: "Discharge" },
      { name: "hearingDecreased", label: "Hearing Decreased" },
      { name: "hearingAidsUse", label: "Use/non-use of hearing aids" },
    ]
  },
  heentNose: {
    radio: [
      { name: "nasalDischarge", label: "Discharge" },
      { name: "nasalItching", label: "Itching" },
      { name: "frequentColds", label: "Frequent Colds" },
      { name: "hayfever", label: "Hayfever" },
      { name: "nasalStuffiness", label: "Nasal Stuffiness" },
      { name: "nosebleeds", label: "Nosebleeds" },
      { name: "sinusPressurePain", label: "Sinus pressure/pain" },
    ]
  },
  heentThroat: {
    radio: [
      { name: "dentalCondition", label: "Condition of teeth and gum" },
      { name: "gumProblemsBleeding", label: "Gum Problems/ Bleeding" },
      { name: "dentures", label: "Dentures (if any)" },
      { name: "soreTongue", label: "Sore Tongue" },
      { name: "dryMouth", label: "Dry Mouth" },
      { name: "frequentSoreThroats", label: "Frequent Sore Throats" },
      { name: "hoarseness", label: "Hoarseness" },
    ],
    input: [
      { name: "denturesFit", label: "Fit of Dentures", placeholder: "Describe fit of dentures", type: "text" },
    ],
    date: [
      { name: "lastDentalExam", label: "Last Dental Exam", placeholder: "Enter date of last dental exam", type: "date" },
    ]
  },
  neck: {
    radio: [
      { name: "swollenGlands", label: "Swollen Glands" },
      { name: "thyroidProblems", label: "Thyroid Problems" },
      { name: "goiter", label: "Goiter" },
      { name: "neckLumps", label: "Lumps" },
      { name: "neckPainStiffness", label: "Pain or Stiffness" },
    ]
  },
  breasts: {
    radio: [
      { name: "breastLumps", label: "Lumps" },
      { name: "breastPainDiscomfort", label: "Pain or discomfort" },
      { name: "nippleDischarge", label: "Nipple discharge" },
      { name: "selfExamPractices", label: "Self-exam practices" },
    ]
  },
  respiratory: {
    radio: [
      { name: "cough", label: "Cough" },
      { name: "sputum", label: "Sputum" },
      { name: "sputumBlood", label: "Presence of blood" },
      { name: "shortnessOfBreath_respiratory", label: "Shortness of breath (Dyspnea)" },
      { name: "wheezing", label: "Wheezing" },
      { name: "pleuriticPain", label: "Pain with a deep breath (Pleuritic pain)" },
      { name: "asthma", label: "Asthma" },
      { name: "bronchitis", label: "Bronchitis" },
      { name: "emphysema", label: "Emphysema" },
      { name: "pneumonia", label: "Pneumonia" },
      { name: "tuberculosis", label: "Tuberculosis" },
    ],
    input: [
      { name: "sputumColor", label: "Sputum Color", placeholder: "Describe sputum color", type: "text" },
      { name: "sputumQuantity", label: "Sputum Quantity", placeholder: "Describe sputum quantity", type: "text" },
    ],
    date: [
      { name: "lastChestXray", label: "Last Chest X-Ray", placeholder: "Enter date of last chest X-ray", type: "date" },
    ]
  },
  cardiovascular: {
    radio: [
      { name: "heartTrouble", label: "Heart Trouble" },
      { name: "highBloodPressure", label: "High Blood Pressure" },
      { name: "rheumaticFever", label: "Rheumatic Fever" },
      { name: "heartMurmurs", label: "Heart Murmurs" },
      { name: "chestPain", label: "Chest pain or discomfort" },
      { name: "palpitations", label: "Palpitations" },
      { name: "shortnessOfBreath_cardio", label: "Shortness of Breath" },
      { name: "orthopnea", label: "Use of pillows or head of bed elevation at night to ease breathing (Orthopnea)" },
      { name: "paroxysmalNocturnalDyspnea", label: "Need to sit up at night to ease breathing (paroxysmal nocturnal dyspnea)" },
      { name: "edema", label: "Swelling in the hands, ankles or feet (edema)" },
    ],
    input: [
      { name: "ekgOther", label: "Results of past EKG or other Cardiovascular tests EKG/ Other:", placeholder: "Value", type: "text" },
    ]
  },
  gastrointestinal: {
    radio: [
      { name: "unintentionalWeightChange", label: "Unintentional Weight loss/ gain" },
      { name: "troubleSwallowing", label: "Trouble swallowing" },
      { name: "heartburn", label: "Heartburn" },
      { name: "appetite", label: "Appetite" },
      { name: "specialDiet", label: "On Special Diet" },
      { name: "nausea", label: "Nausea" },
      { name: "changeInBowelHabits", label: "Change in Bowel Habits" },
      { name: "painWithDefecation", label: "Pain with Defecation" },
      { name: "rectalBleeding", label: "Rectal Bleeding" },
      { name: "blackOrTarryStools", label: "Black or Tarry Stools" },
      { name: "hemorrhoids", label: "Hemorrhoids" },
      { name: "constipation", label: "Constipation" },
      { name: "diarrhea", label: "Diarrhea/Irritable Bowel Syndrome" },
      { name: "abdominalPain", label: "Abdominal Pain" },
      { name: "foodIntolerance", label: "Food Intolerance" },
      { name: "excessiveBelching", label: "Excessive belching or passing of gas" },
      { name: "jaundice", label: "Jaundice (yellowing of skin)" },
      { name: "liverGallbladderTrouble", label: "Liver or Gallbladder Trouble" },
      { name: "hepatitis", label: "Hepatitis" },
    ],
    input: [
      { name: "stoolColor", label: "Stool Color", placeholder: "Describe stool color", type: "text" },
      { name: "stoolSize", label: "Stool Size", placeholder: "Describe stool size", type: "text" },
    ]
  },
  peripheralVascular: {
    radio: [
      { name: "claudication", label: "Intermittent leg pain with exertion (claudication)" },
      { name: "legCramps", label: "Leg Cramps" },
      { name: "varicoseVeins", label: "Varicose Veins" },
      { name: "pastClots", label: "Past clots in the veins" },
      { name: "calfLegFeetSwelling", label: "Swelling in the calves, legs, or feet" },
      { name: "fingertipColorChangeCold", label: "Color change in fingertips/toes during cold weather" },
      { name: "swellingRednessTendernessPeripheral", label: "Swelling with redness/tenderness" },
    ]
  },
  urinary: {
    radio: [
      { name: "frequencyOfUrination", label: "Frequency of Urination" },
      { name: "polyuria", label: "Polyuria" },
      { name: "nocturia", label: "Nocturia" },
      { name: "urgency", label: "Urgency" },
      { name: "burningPainDuringUrination", label: "Burning or Pain During Urination" },
      { name: "hematuria", label: "Hematuria" },
      { name: "urinaryInfections", label: "Urinary infections" },
      { name: "kidneyOrFlankPain", label: "Kidney or Flank Pain" },
      { name: "kidneyStones", label: "Kidney Stones" },
      { name: "ureteralColic", label: "Ureteral Colic" },
      { name: "suprapubicPain", label: "Suprapubic Pain" },
      { name: "incontinence", label: "Incontinence" },
      { name: "reducedUrinaryCaliberOrForce", label: "Reduced caliber or force of the urinary system" },
      { name: "hesitancy", label: "Hesitancy" },
      { name: "dribbling", label: "Dribbling" },
    ]
  },
  genitalMale: {
    radio: [
      { name: "hernias", label: "Hernias" },
      { name: "penileDischarge", label: "Discharge from sores on the penis" },
      { name: "testicularPainOrMasses", label: "Testicular Pain or Masses" },
      { name: "scrotalPainOrSwelling", label: "Scrotal Pain or Swelling" },
    ]
  },
  stdHistory: {
    radio: [
      { name: "stdHistory", label: "History of Sexually Transmitted infections" },
    ],
    input: [
      { name: "stdTreatment", label: "Treatment", placeholder: "Describe STD treatment", type: "text" },
    ]
  },
  sexualHabits: {
    radio: [
      { name: "sexualInterest", label: "Interest" },
      { name: "sexualFunction", label: "Function" },
      { name: "sexualSatisfaction", label: "Satisfaction" },
      { name: "birthControlMethodUse", label: "Birth Control Methods" },
      { name: "condomUse", label: "Condom Use" },
      { name: "sexualProblems", label: "Problems" },
      { name: "hivInfectionConcerns", label: "Concerns about HIV Infection" },
    ]
  },
  genitalFemale: {
    radio: [
      { name: "periodRegularity", label: "Period Regularity" },
      { name: "bleedingBetweenPeriods", label: "Bleeding Between Periods" },
      { name: "bleedingAfterIntercourse", label: "Bleeding After Intercourse" },
      { name: "dysmenorrhea", label: "Dysmenorrhea" },
      { name: "premenstrualTension", label: "Premenstrual Tension" },
      { name: "menopausalSymptoms", label: "Menopausal Symptoms" },
      { name: "postMenopausalBleeding", label: "Post-Menopausal Bleeding" },
      { name: "vaginalDischarge", label: "Vaginal Discharge" },
      { name: "itching_female", label: "Itching" },
      { name: "sores_female", label: "Sores" },
      { name: "lumps_female", label: "Lumps" },
      { name: "historyOfSexuallyTransmittedInfections_female", label: "History of Sexually Transmitted infections" },
      { name: "complicationsOfPregnancy", label: "Complications of pregnancy" },
    ],
    input: [
      { name: "ageAtMenarche", label: "Age at Menarche", placeholder: "Enter age", type: "text" },
      { name: "periodFrequency", label: "Period Frequency", placeholder: "Enter frequency", type: "text" },
      { name: "durationOfPeriods", label: "Duration of Periods", placeholder: "Enter duration", type: "text" },
      { name: "amountOfBleeding", label: "Amount of Bleeding", placeholder: "Describe amount", type: "text" },
      { name: "ageAtMenopause", label: "Age at Menopause", placeholder: "Enter age", type: "text" },
      { name: "treatmentOfSexuallyTransmittedInfections_female", label: "Treatment", placeholder: "value", type: "text" },
      { name: "numberOfPregnancies", label: "Number of pregnancies", placeholder: "value", type: "text" },
      { name: "numberOfDeliveries", label: "Number of deliveries", placeholder: "value", type: "text" },
      { name: "typeOfDeliveries", label: "Type of deliveries", placeholder: "value", type: "text" },
      { name: "numberOfAbortions", label: "Number of Abortions", placeholder: "value", type: "text" },
      { name: "birthControlMethods", label: "Birth Control Methods", placeholder: "value", type: "text" },
    ],
    date: [
      { name: "lastMenstrualPeriod", label: "Last Menstrual Period", placeholder: "Enter date", type: "date" },
    ]
  },
  sexualHabitsFemale: {
    radio: [
      { name: "sexualInterest_female", label: "Interest" },
      { name: "sexualFunction_female", label: "Function" },
      { name: "sexualSatisfaction_female", label: "Satisfaction" },
      { name: "anyProblemsIncludingDyspareunia_female", label: "Any problems including dyspareunia" },
      { name: "concernsAboutHivInfection_female", label: "Concerns about HIV infection" },
    ]
  },
  musculoskeletal: {
    radio: [
      { name: "muscleOrJointPain", label: "Muscle or joint pain" },
      { name: "stiffness", label: "Stiffness" },
      { name: "arthritis", label: "Arthritis" },
      { name: "gout", label: "Gout" },
      { name: "backache", label: "Backache" },
      { name: "swelling_muscle", label: "Swelling" },
      { name: "redness_muscle", label: "Redness" },
      { name: "painMusculoskeletal", label: "Pain" },
      { name: "tenderness_muscle", label: "Tenderness" },
      { name: "weaknessMusculoskeletal", label: "Weakness" },
      { name: "numbnessInLimb", label: "Numbness in Limb" },
      { name: "limitationOfMotionOrActivity", label: "Limitation of Motion or Activity" },
      { name: "timingMorning", label: "Morning" },
      { name: "timingEvening", label: "Evening" },
      { name: "historyOfTrauma", label: "History of Trauma" },
      { name: "neckOrLowBackPain", label: "Neck or Low Back Pain" },
      { name: "systemicJointPain", label: "Joint pain with systemic symptoms such as fever, chills, rash, anorexia, weight loss or weakness" },
    ],
    input: [
      { name: "muscleLocationDescription", label: "If present, describe location of affected joints/muscles:", placeholder: "Describe affected areas", type: "text" },
      { name: "duration", label: "Duration", placeholder: "Enter duration", type: "text" },
    ]
  },
  psychiatric: {
    radio: [
      { name: "nervousness", label: "Nervousness" },
      { name: "tension", label: "Tension" },
      { name: "feelingDownSadDepressed", label: "Feeling Down, Sad, or Depressed" },
      { name: "memoryChange", label: "Memory Change" },
      { name: "suicidePlansOrAttempts", label: "Suicide Plans or Attempts" },
      { name: "suicidalThoughts", label: "Suicidal Thoughts" },
      { name: "pastCounseling", label: "Past Counseling" },
      { name: "psychiatricAdmissions", label: "Psychiatric Admissions" },
      { name: "onPsychiatricMedications", label: "On Psychiatric Medications" },
    ]
  },
  neurologic: {
    radio: [
      { name: "changeInMoodNeurologic", label: "Change in Mood" },
      { name: "changeInAttention", label: "Change in Attention" },
      { name: "changeInSpeech", label: "Change in Speech" },
      { name: "changeInOrientation", label: "Change in Orientation" },
      { name: "changeInMemoryNeurologic", label: "Change in Memory" },
      { name: "changeInInsight", label: "Change in Insight" },
      { name: "changeInJudgement", label: "Change in Judgement" },
      { name: "headacheNeurologic", label: "Headache" },
      { name: "dizzinessNeurologic", label: "Dizziness" },
      { name: "vertigoNeurologic", label: "Vertigo" },
      { name: "fainting", label: "Fainting" },
      { name: "blackouts", label: "Blackouts" },
      { name: "weaknessNeurologic", label: "Weakness" },
      { name: "paralysis", label: "Paralysis" },
      { name: "numbnessOrLossOfSensation", label: "Numbness or Loss of Sensation" },
      { name: "tinglingPinsAndNeedles", label: "Tingling or Pins and Needles" },
      { name: "tremorsOrOtherInvoluntaryMovement", label: "Tremors or Other involuntary movement" },
      { name: "seizures", label: "Seizures" },
    ]
  },
  hematologic: {
    radio: [
      { name: "anemia", label: "Anemia" },
      { name: "easyBruisingOrBleeding", label: "Easy bruising or bleeding" },
      { name: "pastTransfusions", label: "Past transfusions" },
      { name: "transfusionReactions", label: "Transfusion reactions" },
    ]
  },
  endocrine: {
    radio: [
      { name: "thyroidTrouble", label: "Thyroid trouble" },
      { name: "heatOrColdIntolerance", label: "Heat or Cold Intolerance" },
      { name: "excessiveSweating", label: "Excessive Sweating" },
      { name: "excessiveThirstOrHunger", label: "Excessive thirst or hunger" },
      { name: "polyuriaEndocrine", label: "Polyuria" },
      { name: "changeInGloveOrShoeSize", label: "Change in glove or shoe size" },
    ]
  },
};
