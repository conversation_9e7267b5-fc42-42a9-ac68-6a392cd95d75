import React, { useState, useEffect } from "react";
import { useLocation } from "react-router-dom";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { RadioGroup, RadioGroupItem } from "../../components/ui/radio-group-2";
import { Input } from "../../components/ui/input";
import { Button } from "../../components/ui/button";
import { Textarea } from "../../components/ui/textarea";
import { useToast } from "../../hooks/use-toast";
import { Loader2 } from "lucide-react";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "../../components/ui/form";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "../../components/ui/accordion-availability";
import {
  createRosForm_apiCalls,
  updateRosForm_apiCalls,
  getRosFormByAppointmentId_apiCalls,
} from "../../api/api_calls/appointmentforms/rosForm_apiCalls";

const rosFormSchema = z.object({
  appointmentId: z.string(),
  id: z.string().optional(),
  // GENERAL
  recentWeightChange: z.boolean().nullable().optional(),
  weakness: z.boolean().nullable().optional(),
  fatigue: z.boolean().nullable().optional(),
  fever: z.boolean().nullable().optional(),

  // SKIN
  skinRashes: z.boolean().nullable().optional(),
  skinLumps: z.boolean().nullable().optional(),
  skinSores: z.boolean().nullable().optional(),
  skinItching: z.boolean().nullable().optional(),
  skinDryness: z.boolean().nullable().optional(),
  skinChangeInColor: z.boolean().nullable().optional(),
  skinChangeInHair: z.boolean().nullable().optional(),
  skinChangeInMoles: z.boolean().nullable().optional(),

  // HEENT - Head
  headache: z.boolean().nullable().optional(),
  headInjury: z.boolean().nullable().optional(),
  dizziness: z.boolean().nullable().optional(),
  lightheadedness: z.boolean().nullable().optional(),

  // HEENT - Eyes
  visionChange: z.boolean().nullable().optional(),
  glassesOrContactLenses: z.boolean().nullable().optional(),
  eyePain: z.boolean().nullable().optional(),
  eyeRedness: z.boolean().nullable().optional(),
  excessiveTearing: z.boolean().nullable().optional(),
  doubleOrBlurredVision: z.boolean().nullable().optional(),
  spots: z.boolean().nullable().optional(),
  specks: z.boolean().nullable().optional(),
  flashingLights: z.boolean().nullable().optional(),
  glaucoma: z.boolean().nullable().optional(),
  cataracts: z.boolean().nullable().optional(),

  // HEENT - Ears
  hearingLoss: z.boolean().nullable().optional(),
  tinnitus: z.boolean().nullable().optional(),
  vertigo: z.boolean().nullable().optional(),
  earaches: z.boolean().nullable().optional(),
  earInfection: z.boolean().nullable().optional(),
  earDischarge: z.boolean().nullable().optional(),
  hearingDecreased: z.boolean().nullable().optional(),
  hearingAidsUse: z.boolean().nullable().optional(),

  // HEENT - Nose and Sinuses
  nasalDischarge: z.boolean().nullable().optional(),
  nasalItching: z.boolean().nullable().optional(),
  frequentColds: z.boolean().nullable().optional(),
  hayfever: z.boolean().nullable().optional(),
  nasalStuffiness: z.boolean().nullable().optional(),
  nosebleeds: z.boolean().nullable().optional(),
  sinusPressurePain: z.boolean().nullable().optional(),

  // HEENT - Throat
  dentalCondition: z.boolean().nullable().optional(),
  gumProblemsBleeding: z.boolean().nullable().optional(),
  dentures: z.boolean().nullable().optional(),
  denturesFit: z.string().nullable().optional(),
  lastDentalExam: z.date().nullable().optional(),
  soreTongue: z.boolean().nullable().optional(),
  dryMouth: z.boolean().nullable().optional(),
  frequentSoreThroats: z.boolean().nullable().optional(),
  hoarseness: z.boolean().nullable().optional(),

  // NECK
  swollenGlands: z.boolean().nullable().optional(),
  thyroidProblems: z.boolean().nullable().optional(),
  goiter: z.boolean().nullable().optional(),
  neckLumps: z.boolean().nullable().optional(),
  neckPainStiffness: z.boolean().nullable().optional(),

  // BREASTS
  breastLumps: z.boolean().nullable().optional(),
  breastPainDiscomfort: z.boolean().nullable().optional(),
  nippleDischarge: z.boolean().nullable().optional(),
  selfExamPractices: z.boolean().nullable().optional(),

  // RESPIRATORY
  cough: z.boolean().nullable().optional(),
  sputum: z.boolean().nullable().optional(),
  sputumColor: z.string().nullable().optional(),
  sputumQuantity: z.string().nullable().optional(),
  sputumBlood: z.boolean().nullable().optional(),
  shortnessOfBreath_respiratory: z.boolean().nullable().optional(),
  wheezing: z.boolean().nullable().optional(),
  pleuriticPain: z.boolean().nullable().optional(),
  lastChestXray: z.date().nullable().optional(),
  asthma: z.boolean().nullable().optional(),
  bronchitis: z.boolean().nullable().optional(),
  emphysema: z.boolean().nullable().optional(),
  pneumonia: z.boolean().nullable().optional(),
  tuberculosis: z.boolean().nullable().optional(),

  // CARDIOVASCULAR
  heartTrouble: z.boolean().nullable().optional(),
  highBloodPressure: z.boolean().nullable().optional(),
  rheumaticFever: z.boolean().nullable().optional(),
  heartMurmurs: z.boolean().nullable().optional(),
  chestPain: z.boolean().nullable().optional(),
  palpitations: z.boolean().nullable().optional(),
  shortnessOfBreath_cardio: z.boolean().nullable().optional(),
  orthopnea: z.boolean().nullable().optional(),
  paroxysmalNocturnalDyspnea: z.boolean().nullable().optional(),
  edema: z.boolean().nullable().optional(),
  ekgOther: z.string().nullable().optional(),

  // GASTROINTESTINAL
  unintentionalWeightChange: z.boolean().nullable().optional(),
  troubleSwallowing: z.boolean().nullable().optional(),
  heartburn: z.boolean().nullable().optional(),
  appetite: z.boolean().nullable().optional(),
  specialDiet: z.boolean().nullable().optional(),
  nausea: z.boolean().nullable().optional(),
  stoolColor: z.string().nullable().optional(),
  stoolSize: z.string().nullable().optional(),
  changeInBowelHabits: z.boolean().nullable().optional(),
  painWithDefecation: z.boolean().nullable().optional(),
  rectalBleeding: z.boolean().nullable().optional(),
  blackOrTarryStools: z.boolean().nullable().optional(),
  hemorrhoids: z.boolean().nullable().optional(),
  constipation: z.boolean().nullable().optional(),
  diarrhea: z.boolean().nullable().optional(),
  abdominalPain: z.boolean().nullable().optional(),
  foodIntolerance: z.boolean().nullable().optional(),
  excessiveBelching: z.boolean().nullable().optional(),
  jaundice: z.boolean().nullable().optional(),
  liverGallbladderTrouble: z.boolean().nullable().optional(),
  hepatitis: z.boolean().nullable().optional(),

  // PERIPHERAL VASCULAR
  claudication: z.boolean().nullable().optional(),
  legCramps: z.boolean().nullable().optional(),
  varicoseVeins: z.boolean().nullable().optional(),
  pastClots: z.boolean().nullable().optional(),
  calfLegFeetSwelling: z.boolean().nullable().optional(),
  fingertipColorChangeCold: z.boolean().nullable().optional(),
  swellingRednessTendernessPeripheral: z.boolean().nullable().optional(),

  // URINARY
  frequencyOfUrination: z.boolean().nullable().optional(),
  polyuria: z.boolean().nullable().optional(),
  nocturia: z.boolean().nullable().optional(),
  urgency: z.boolean().nullable().optional(),
  burningPainDuringUrination: z.boolean().nullable().optional(),
  hematuria: z.boolean().nullable().optional(),
  urinaryInfections: z.boolean().nullable().optional(),
  kidneyOrFlankPain: z.boolean().nullable().optional(),
  kidneyStones: z.boolean().nullable().optional(),
  ureteralColic: z.boolean().nullable().optional(),
  suprapubicPain: z.boolean().nullable().optional(),
  incontinence: z.boolean().nullable().optional(),
  reducedUrinaryCaliberOrForce: z.boolean().nullable().optional(),
  hesitancy: z.boolean().nullable().optional(),
  dribbling: z.boolean().nullable().optional(),

  // GENITAL - MALE
  hernias: z.boolean().nullable().optional(),
  penileDischarge: z.boolean().nullable().optional(),
  testicularPainOrMasses: z.boolean().nullable().optional(),
  scrotalPainOrSwelling: z.boolean().nullable().optional(),

  // STD History
  stdHistory: z.boolean().nullable().optional(),
  stdTreatment: z.string().nullable().optional(),

  // Sexual Habits
  sexualInterest: z.boolean().nullable().optional(),
  sexualFunction: z.boolean().nullable().optional(),
  sexualSatisfaction: z.boolean().nullable().optional(),
  birthControlMethodUse: z.boolean().nullable().optional(),
  condomUse: z.boolean().nullable().optional(),
  sexualProblems: z.boolean().nullable().optional(),
  hivInfectionConcerns: z.boolean().nullable().optional(),

  // GENITAL - FEMALE
  ageAtMenarche: z.string().nullable().optional(),
  periodFrequency: z.string().nullable().optional(),
  durationOfPeriods: z.string().nullable().optional(),
  amountOfBleeding: z.string().nullable().optional(),
  periodRegularity: z.boolean().nullable().optional(),
  bleedingBetweenPeriods: z.boolean().nullable().optional(),
  bleedingAfterIntercourse: z.boolean().nullable().optional(),
  lastMenstrualPeriod: z.date().nullable().optional(),
  dysmenorrhea: z.boolean().nullable().optional(),
  premenstrualTension: z.boolean().nullable().optional(),
  ageAtMenopause: z.string().nullable().optional(),
  menopausalSymptoms: z.boolean().nullable().optional(),
  postMenopausalBleeding: z.boolean().nullable().optional(),
  vaginalDischarge: z.boolean().nullable().optional(),
  itching_female: z.boolean().nullable().optional(),
  sores_female: z.boolean().nullable().optional(),
  lumps_female: z.boolean().nullable().optional(),
  historyOfSexuallyTransmittedInfections_female: z
    .boolean()
    .nullable()
    .optional(),
  treatmentOfSexuallyTransmittedInfections_female: z
    .string()
    .nullable()
    .optional(),
  numberOfPregnancies: z.string().nullable().optional(),
  numberOfDeliveries: z.string().nullable().optional(),
  typeOfDeliveries: z.string().nullable().optional(),
  numberOfAbortions: z.string().nullable().optional(),
  birthControlMethods: z.string().nullable().optional(),
  complicationsOfPregnancy: z.boolean().nullable().optional(),

  // SEXUAL HABITS (FEMALE-SPECIFIC)
  sexualInterest_female: z.boolean().nullable().optional(),
  sexualFunction_female: z.boolean().nullable().optional(),
  sexualSatisfaction_female: z.boolean().nullable().optional(),
  anyProblemsIncludingDyspareunia_female: z.boolean().nullable().optional(),
  concernsAboutHivInfection_female: z.boolean().nullable().optional(),

  // MUSCULOSKELETAL
  muscleOrJointPain: z.boolean().nullable().optional(),
  stiffness: z.boolean().nullable().optional(),
  arthritis: z.boolean().nullable().optional(),
  gout: z.boolean().nullable().optional(),
  backache: z.boolean().nullable().optional(),
  muscleLocationDescription: z.string().nullable().optional(),
  swelling_muscle: z.boolean().nullable().optional(),
  redness_muscle: z.boolean().nullable().optional(),
  painMusculoskeletal: z.boolean().nullable().optional(),
  tenderness_muscle: z.boolean().nullable().optional(),
  weaknessMusculoskeletal: z.boolean().nullable().optional(),
  numbnessInLimb: z.boolean().nullable().optional(),
  limitationOfMotionOrActivity: z.boolean().nullable().optional(),
  timingMorning: z.boolean().nullable().optional(),
  timingEvening: z.boolean().nullable().optional(),
  duration: z.string().nullable().optional(),
  historyOfTrauma: z.boolean().nullable().optional(),
  neckOrLowBackPain: z.boolean().nullable().optional(),
  systemicJointPain: z.boolean().nullable().optional(),

  // PSYCHIATRIC
  nervousness: z.boolean().nullable().optional(),
  tension: z.boolean().nullable().optional(),
  feelingDownSadDepressed: z.boolean().nullable().optional(),
  memoryChange: z.boolean().nullable().optional(),
  suicidePlansOrAttempts: z.boolean().nullable().optional(),
  suicidalThoughts: z.boolean().nullable().optional(),
  pastCounseling: z.boolean().nullable().optional(),
  psychiatricAdmissions: z.boolean().nullable().optional(),
  onPsychiatricMedications: z.boolean().nullable().optional(),

  // NEUROLOGIC
  changeInMoodNeurologic: z.boolean().nullable().optional(),
  changeInAttention: z.boolean().nullable().optional(),
  changeInSpeech: z.boolean().nullable().optional(),
  changeInOrientation: z.boolean().nullable().optional(),
  changeInMemoryNeurologic: z.boolean().nullable().optional(),
  changeInInsight: z.boolean().nullable().optional(),
  changeInJudgement: z.boolean().nullable().optional(),
  headacheNeurologic: z.boolean().nullable().optional(),
  dizzinessNeurologic: z.boolean().nullable().optional(),
  vertigoNeurologic: z.boolean().nullable().optional(),
  fainting: z.boolean().nullable().optional(),
  blackouts: z.boolean().nullable().optional(),
  weaknessNeurologic: z.boolean().nullable().optional(),
  paralysis: z.boolean().nullable().optional(),
  numbnessOrLossOfSensation: z.boolean().nullable().optional(),
  tinglingPinsAndNeedles: z.boolean().nullable().optional(),
  tremorsOrOtherInvoluntaryMovement: z.boolean().nullable().optional(),
  seizures: z.boolean().nullable().optional(),

  // HEMATOLOGIC
  anemia: z.boolean().nullable().optional(),
  easyBruisingOrBleeding: z.boolean().nullable().optional(),
  pastTransfusions: z.boolean().nullable().optional(),
  transfusionReactions: z.boolean().nullable().optional(),

  // ENDOCRINE
  thyroidTrouble: z.boolean().nullable().optional(),
  heatOrColdIntolerance: z.boolean().nullable().optional(),
  excessiveSweating: z.boolean().nullable().optional(),
  excessiveThirstOrHunger: z.boolean().nullable().optional(),
  polyuriaEndocrine: z.boolean().nullable().optional(),
  changeInGloveOrShoeSize: z.boolean().nullable().optional(),
});

// Field configurations for each section based on complete schema
const fieldConfigs = {
  general: {
    radio: [
      { name: "recentWeightChange", label: "Recent Weight Change" },
      { name: "weakness", label: "Weakness" },
      { name: "fatigue", label: "Fatigue" },
      { name: "fever", label: "Fever" },
    ]
  },
  skin: {
    radio: [
      { name: "skinRashes", label: "Rashes" },
      { name: "skinLumps", label: "Lumps" },
      { name: "skinSores", label: "Sores" },
      { name: "skinItching", label: "Itching" },
      { name: "skinDryness", label: "Dryness" },
      { name: "skinChangeInColor", label: "Change in Color" },
      { name: "skinChangeInHair", label: "Change in Hair" },
      { name: "skinChangeInMoles", label: "Change in Moles" },
    ]
  },
  heentHead: {
    radio: [
      { name: "headache", label: "Headache" },
      { name: "headInjury", label: "Head Injury" },
      { name: "dizziness", label: "Dizziness" },
      { name: "lightheadedness", label: "Lightheadedness" },
    ]
  },
  heentEyes: {
    radio: [
      { name: "visionChange", label: "Vision Change" },
      { name: "glassesOrContactLenses", label: "Glasses or Contact Lenses" },
      { name: "eyePain", label: "Eye Pain" },
      { name: "eyeRedness", label: "Eye Redness" },
      { name: "excessiveTearing", label: "Excessive Tearing" },
      { name: "doubleOrBlurredVision", label: "Double or Blurred Vision" },
      { name: "spots", label: "Spots" },
      { name: "specks", label: "Specks" },
      { name: "flashingLights", label: "Flashing Lights" },
      { name: "glaucoma", label: "Glaucoma" },
      { name: "cataracts", label: "Cataracts" },
    ]
  },
  heentEars: {
    radio: [
      { name: "hearingLoss", label: "Hearing Loss" },
      { name: "tinnitus", label: "Ringing in Ear(s)/Tinnitus" },
      { name: "vertigo", label: "Vertigo" },
      { name: "earaches", label: "Earaches" },
      { name: "earInfection", label: "Infection" },
      { name: "earDischarge", label: "Discharge" },
      { name: "hearingDecreased", label: "Hearing Decreased" },
      { name: "hearingAidsUse", label: "Use/non-use of hearing aids" },
    ]
  },
  heentNose: {
    radio: [
      { name: "nasalDischarge", label: "Discharge" },
      { name: "nasalItching", label: "Itching" },
      { name: "frequentColds", label: "Frequent Colds" },
      { name: "hayfever", label: "Hayfever" },
      { name: "nasalStuffiness", label: "Nasal Stuffiness" },
      { name: "nosebleeds", label: "Nosebleeds" },
      { name: "sinusPressurePain", label: "Sinus pressure/pain" },
    ]
  },
  heentThroat: {
    radio: [
      { name: "dentalCondition", label: "Condition of teeth and gum" },
      { name: "gumProblemsBleeding", label: "Gum Problems/ Bleeding" },
      { name: "dentures", label: "Dentures (if any)" },
      { name: "soreTongue", label: "Sore Tongue" },
      { name: "dryMouth", label: "Dry Mouth" },
      { name: "frequentSoreThroats", label: "Frequent Sore Throats" },
      { name: "hoarseness", label: "Hoarseness" },
    ],
    input: [
      { name: "denturesFit", label: "Fit of Dentures", placeholder: "Describe fit of dentures", type: "text" },
    ],
    date: [
      { name: "lastDentalExam", label: "Last Dental Exam", placeholder: "Enter date of last dental exam", type: "date" },
    ]
  },
  neck: {
    radio: [
      { name: "swollenGlands", label: "Swollen Glands" },
      { name: "thyroidProblems", label: "Thyroid Problems" },
      { name: "goiter", label: "Goiter" },
      { name: "neckLumps", label: "Lumps" },
      { name: "neckPainStiffness", label: "Pain or Stiffness" },
    ]
  },
  breasts: {
    radio: [
      { name: "breastLumps", label: "Lumps" },
      { name: "breastPainDiscomfort", label: "Pain or discomfort" },
      { name: "nippleDischarge", label: "Nipple discharge" },
      { name: "selfExamPractices", label: "Self-exam practices" },
    ]
  },
  respiratory: {
    radio: [
      { name: "cough", label: "Cough" },
      { name: "sputum", label: "Sputum" },
      { name: "sputumBlood", label: "Presence of blood" },
      { name: "shortnessOfBreath_respiratory", label: "Shortness of breath (Dyspnea)" },
      { name: "wheezing", label: "Wheezing" },
      { name: "pleuriticPain", label: "Pain with a deep breath (Pleuritic pain)" },
      { name: "asthma", label: "Asthma" },
      { name: "bronchitis", label: "Bronchitis" },
      { name: "emphysema", label: "Emphysema" },
      { name: "pneumonia", label: "Pneumonia" },
      { name: "tuberculosis", label: "Tuberculosis" },
    ],
    input: [
      { name: "sputumColor", label: "Sputum Color", placeholder: "Describe sputum color", type: "text" },
      { name: "sputumQuantity", label: "Sputum Quantity", placeholder: "Describe sputum quantity", type: "text" },
    ],
    date: [
      { name: "lastChestXray", label: "Last Chest X-Ray", placeholder: "Enter date of last chest X-ray", type: "date" },
    ]
  },
  cardiovascular: {
    radio: [
      { name: "heartTrouble", label: "Heart Trouble" },
      { name: "highBloodPressure", label: "High Blood Pressure" },
      { name: "rheumaticFever", label: "Rheumatic Fever" },
      { name: "heartMurmurs", label: "Heart Murmurs" },
      { name: "chestPain", label: "Chest pain or discomfort" },
      { name: "palpitations", label: "Palpitations" },
      { name: "shortnessOfBreath_cardio", label: "Shortness of Breath" },
      { name: "orthopnea", label: "Use of pillows or head of bed elevation at night to ease breathing (Orthopnea)" },
      { name: "paroxysmalNocturnalDyspnea", label: "Need to sit up at night to ease breathing (paroxysmal nocturnal dyspnea)" },
      { name: "edema", label: "Swelling in the hands, ankles or feet (edema)" },
    ],
    input: [
      { name: "ekgOther", label: "Results of past EKG or other Cardiovascular tests EKG/ Other:", placeholder: "Value", type: "text" },
    ]
  },
  gastrointestinal: {
    radio: [
      { name: "unintentionalWeightChange", label: "Unintentional Weight loss/ gain" },
      { name: "troubleSwallowing", label: "Trouble swallowing" },
      { name: "heartburn", label: "Heartburn" },
      { name: "appetite", label: "Appetite" },
      { name: "specialDiet", label: "On Special Diet" },
      { name: "nausea", label: "Nausea" },
      { name: "changeInBowelHabits", label: "Change in Bowel Habits" },
      { name: "painWithDefecation", label: "Pain with Defecation" },
      { name: "rectalBleeding", label: "Rectal Bleeding" },
      { name: "blackOrTarryStools", label: "Black or Tarry Stools" },
      { name: "hemorrhoids", label: "Hemorrhoids" },
      { name: "constipation", label: "Constipation" },
      { name: "diarrhea", label: "Diarrhea/Irritable Bowel Syndrome" },
      { name: "abdominalPain", label: "Abdominal Pain" },
      { name: "foodIntolerance", label: "Food Intolerance" },
      { name: "excessiveBelching", label: "Excessive belching or passing of gas" },
      { name: "jaundice", label: "Jaundice (yellowing of skin)" },
      { name: "liverGallbladderTrouble", label: "Liver or Gallbladder Trouble" },
      { name: "hepatitis", label: "Hepatitis" },
    ],
    input: [
      { name: "stoolColor", label: "Stool Color", placeholder: "Describe stool color", type: "text" },
      { name: "stoolSize", label: "Stool Size", placeholder: "Describe stool size", type: "text" },
    ]
  },
  peripheralVascular: {
    radio: [
      { name: "claudication", label: "Intermittent leg pain with exertion (claudication)" },
      { name: "legCramps", label: "Leg Cramps" },
      { name: "varicoseVeins", label: "Varicose Veins" },
      { name: "pastClots", label: "Past clots in the veins" },
      { name: "calfLegFeetSwelling", label: "Swelling in the calves, legs, or feet" },
      { name: "fingertipColorChangeCold", label: "Color change in fingertips/toes during cold weather" },
      { name: "swellingRednessTendernessPeripheral", label: "Swelling with redness/tenderness" },
    ]
  },
  urinary: {
    radio: [
      { name: "frequencyOfUrination", label: "Frequency of Urination" },
      { name: "polyuria", label: "Polyuria" },
      { name: "nocturia", label: "Nocturia" },
      { name: "urgency", label: "Urgency" },
      { name: "burningPainDuringUrination", label: "Burning or Pain During Urination" },
      { name: "hematuria", label: "Hematuria" },
      { name: "urinaryInfections", label: "Urinary infections" },
      { name: "kidneyOrFlankPain", label: "Kidney or Flank Pain" },
      { name: "kidneyStones", label: "Kidney Stones" },
      { name: "ureteralColic", label: "Ureteral Colic" },
      { name: "suprapubicPain", label: "Suprapubic Pain" },
      { name: "incontinence", label: "Incontinence" },
      { name: "reducedUrinaryCaliberOrForce", label: "Reduced caliber or force of the urinary system" },
      { name: "hesitancy", label: "Hesitancy" },
      { name: "dribbling", label: "Dribbling" },
    ]
  },
  genitalMale: {
    radio: [
      { name: "hernias", label: "Hernias" },
      { name: "penileDischarge", label: "Discharge from sores on the penis" },
      { name: "testicularPainOrMasses", label: "Testicular Pain or Masses" },
      { name: "scrotalPainOrSwelling", label: "Scrotal Pain or Swelling" },
    ]
  },
  stdHistory: {
    radio: [
      { name: "stdHistory", label: "History of Sexually Transmitted infections" },
    ],
    input: [
      { name: "stdTreatment", label: "Treatment", placeholder: "Describe STD treatment", type: "text" },
    ]
  },
  sexualHabits: {
    radio: [
      { name: "sexualInterest", label: "Interest" },
      { name: "sexualFunction", label: "Function" },
      { name: "sexualSatisfaction", label: "Satisfaction" },
      { name: "birthControlMethodUse", label: "Birth Control Methods" },
      { name: "condomUse", label: "Condom Use" },
      { name: "sexualProblems", label: "Problems" },
      { name: "hivInfectionConcerns", label: "Concerns about HIV Infection" },
    ]
  },
  genitalFemale: {
    radio: [
      { name: "periodRegularity", label: "Period Regularity" },
      { name: "bleedingBetweenPeriods", label: "Bleeding Between Periods" },
      { name: "bleedingAfterIntercourse", label: "Bleeding After Intercourse" },
      { name: "dysmenorrhea", label: "Dysmenorrhea" },
      { name: "premenstrualTension", label: "Premenstrual Tension" },
      { name: "menopausalSymptoms", label: "Menopausal Symptoms" },
      { name: "postMenopausalBleeding", label: "Post-Menopausal Bleeding" },
      { name: "vaginalDischarge", label: "Vaginal Discharge" },
      { name: "itching_female", label: "Itching" },
      { name: "sores_female", label: "Sores" },
      { name: "lumps_female", label: "Lumps" },
      { name: "historyOfSexuallyTransmittedInfections_female", label: "History of Sexually Transmitted infections" },
      { name: "complicationsOfPregnancy", label: "Complications of pregnancy" },
    ],
    input: [
      { name: "ageAtMenarche", label: "Age at Menarche", placeholder: "Enter age", type: "text" },
      { name: "periodFrequency", label: "Period Frequency", placeholder: "Enter frequency", type: "text" },
      { name: "durationOfPeriods", label: "Duration of Periods", placeholder: "Enter duration", type: "text" },
      { name: "amountOfBleeding", label: "Amount of Bleeding", placeholder: "Describe amount", type: "text" },
      { name: "ageAtMenopause", label: "Age at Menopause", placeholder: "Enter age", type: "text" },
      { name: "treatmentOfSexuallyTransmittedInfections_female", label: "Treatment", placeholder: "value", type: "text" },
      { name: "numberOfPregnancies", label: "Number of pregnancies", placeholder: "value", type: "text" },
      { name: "numberOfDeliveries", label: "Number of deliveries", placeholder: "value", type: "text" },
      { name: "typeOfDeliveries", label: "Type of deliveries", placeholder: "value", type: "text" },
      { name: "numberOfAbortions", label: "Number of Abortions", placeholder: "value", type: "text" },
      { name: "birthControlMethods", label: "Birth Control Methods", placeholder: "value", type: "text" },
    ],
    date: [
      { name: "lastMenstrualPeriod", label: "Last Menstrual Period", placeholder: "Enter date", type: "date" },
    ]
  },
  sexualHabitsFemale: {
    radio: [
      { name: "sexualInterest_female", label: "Interest" },
      { name: "sexualFunction_female", label: "Function" },
      { name: "sexualSatisfaction_female", label: "Satisfaction" },
      { name: "anyProblemsIncludingDyspareunia_female", label: "Any problems including dyspareunia" },
      { name: "concernsAboutHivInfection_female", label: "Concerns about HIV infection" },
    ]
  },
  musculoskeletal: {
    radio: [
      { name: "muscleOrJointPain", label: "Muscle or joint pain" },
      { name: "stiffness", label: "Stiffness" },
      { name: "arthritis", label: "Arthritis" },
      { name: "gout", label: "Gout" },
      { name: "backache", label: "Backache" },
      { name: "swelling_muscle", label: "Swelling" },
      { name: "redness_muscle", label: "Redness" },
      { name: "painMusculoskeletal", label: "Pain" },
      { name: "tenderness_muscle", label: "Tenderness" },
      { name: "weaknessMusculoskeletal", label: "Weakness" },
      { name: "numbnessInLimb", label: "Numbness in Limb" },
      { name: "limitationOfMotionOrActivity", label: "Limitation of Motion or Activity" },
      { name: "timingMorning", label: "Morning" },
      { name: "timingEvening", label: "Evening" },
      { name: "historyOfTrauma", label: "History of Trauma" },
      { name: "neckOrLowBackPain", label: "Neck or Low Back Pain" },
      { name: "systemicJointPain", label: "Joint pain with systemic symptoms such as fever, chills, rash, anorexia, weight loss or weakness" },
    ],
    input: [
      { name: "muscleLocationDescription", label: "If present, describe location of affected joints/muscles:", placeholder: "Describe affected areas", type: "text" },
      { name: "duration", label: "Duration", placeholder: "Enter duration", type: "text" },
    ]
  },
  psychiatric: {
    radio: [
      { name: "nervousness", label: "Nervousness" },
      { name: "tension", label: "Tension" },
      { name: "feelingDownSadDepressed", label: "Feeling Down, Sad, or Depressed" },
      { name: "memoryChange", label: "Memory Change" },
      { name: "suicidePlansOrAttempts", label: "Suicide Plans or Attempts" },
      { name: "suicidalThoughts", label: "Suicidal Thoughts" },
      { name: "pastCounseling", label: "Past Counseling" },
      { name: "psychiatricAdmissions", label: "Psychiatric Admissions" },
      { name: "onPsychiatricMedications", label: "On Psychiatric Medications" },
    ]
  },
  neurologic: {
    radio: [
      { name: "changeInMoodNeurologic", label: "Change in Mood" },
      { name: "changeInAttention", label: "Change in Attention" },
      { name: "changeInSpeech", label: "Change in Speech" },
      { name: "changeInOrientation", label: "Change in Orientation" },
      { name: "changeInMemoryNeurologic", label: "Change in Memory" },
      { name: "changeInInsight", label: "Change in Insight" },
      { name: "changeInJudgement", label: "Change in Judgement" },
      { name: "headacheNeurologic", label: "Headache" },
      { name: "dizzinessNeurologic", label: "Dizziness" },
      { name: "vertigoNeurologic", label: "Vertigo" },
      { name: "fainting", label: "Fainting" },
      { name: "blackouts", label: "Blackouts" },
      { name: "weaknessNeurologic", label: "Weakness" },
      { name: "paralysis", label: "Paralysis" },
      { name: "numbnessOrLossOfSensation", label: "Numbness or Loss of Sensation" },
      { name: "tinglingPinsAndNeedles", label: "Tingling or Pins and Needles" },
      { name: "tremorsOrOtherInvoluntaryMovement", label: "Tremors or Other involuntary movement" },
      { name: "seizures", label: "Seizures" },
    ]
  },
  hematologic: {
    radio: [
      { name: "anemia", label: "Anemia" },
      { name: "easyBruisingOrBleeding", label: "Easy bruising or bleeding" },
      { name: "pastTransfusions", label: "Past transfusions" },
      { name: "transfusionReactions", label: "Transfusion reactions" },
    ]
  },
  endocrine: {
    radio: [
      { name: "thyroidTrouble", label: "Thyroid trouble" },
      { name: "heatOrColdIntolerance", label: "Heat or Cold Intolerance" },
      { name: "excessiveSweating", label: "Excessive Sweating" },
      { name: "excessiveThirstOrHunger", label: "Excessive thirst or hunger" },
      { name: "polyuriaEndocrine", label: "Polyuria" },
      { name: "changeInGloveOrShoeSize", label: "Change in glove or shoe size" },
    ]
  },
};

const ROS = ({ appointmentId }) => {
  const location = useLocation();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [loading, setLoading] = useState(true);
  const [existingRosForm, setExistingRosForm] = useState(null);
  const { toast } = useToast();

  // Get appointmentId from props or location state as fallback
  const finalAppointmentId =
    appointmentId ||
    location.state?.appointmentId ||
    location.state?.appointment?.id;

  const form = useForm({
    resolver: zodResolver(rosFormSchema),
    defaultValues: {
      appointmentId: finalAppointmentId || "",

      // GENERAL
      recentWeightChange: null,
      weakness: null,
      fatigue: null,
      fever: null,

      // SKIN
      skinRashes: null,
      skinLumps: null,
      skinSores: null,
      skinItching: null,
      skinDryness: null,
      skinChangeInColor: null,
      skinChangeInHair: null,
      skinChangeInMoles: null,

      // HEENT - Head
      headache: null,
      headInjury: null,
      dizziness: null,
      lightheadedness: null,

      // HEENT - Eyes
      visionChange: null,
      glassesOrContactLenses: null,
      eyePain: null,
      eyeRedness: null,
      excessiveTearing: null,
      doubleOrBlurredVision: null,
      spots: null,
      specks: null,
      flashingLights: null,
      glaucoma: null,
      cataracts: null,

      // HEENT - Ears
      hearingLoss: null,
      tinnitus: null,
      vertigo: null,
      earaches: null,
      earInfection: null,
      earDischarge: null,
      hearingDecreased: null,
      hearingAidsUse: null,

      // HEENT - Nose and Sinuses
      nasalDischarge: null,
      nasalItching: null,
      frequentColds: null,
      hayfever: null,
      nasalStuffiness: null,
      nosebleeds: null,
      sinusPressurePain: null,

      // HEENT - Throat
      dentalCondition: null,
      gumProblemsBleeding: null,
      dentures: null,
      denturesFit: null,
      lastDentalExam: null,
      soreTongue: null,
      dryMouth: null,
      frequentSoreThroats: null,
      hoarseness: null,

      // NECK
      swollenGlands: null,
      thyroidProblems: null,
      goiter: null,
      neckLumps: null,
      neckPainStiffness: null,

      // BREASTS
      breastLumps: null,
      breastPainDiscomfort: null,
      nippleDischarge: null,
      selfExamPractices: null,

      // RESPIRATORY
      cough: null,
      sputum: null,
      sputumColor: null,
      sputumQuantity: null,
      sputumBlood: null,
      shortnessOfBreath_respiratory: null,
      wheezing: null,
      pleuriticPain: null,
      lastChestXray: null,
      asthma: null,
      bronchitis: null,
      emphysema: null,
      pneumonia: null,
      tuberculosis: null,

      // CARDIOVASCULAR
      heartTrouble: null,
      highBloodPressure: null,
      rheumaticFever: null,
      heartMurmurs: null,
      chestPain: null,
      palpitations: null,
      shortnessOfBreath_cardio: null,
      orthopnea: null,
      paroxysmalNocturnalDyspnea: null,
      edema: null,
      ekgOther: null,

      // GASTROINTESTINAL
      unintentionalWeightChange: null,
      troubleSwallowing: null,
      heartburn: null,
      appetite: null,
      specialDiet: null,
      nausea: null,
      stoolColor: null,
      stoolSize: null,
      changeInBowelHabits: null,
      painWithDefecation: null,
      rectalBleeding: null,
      blackOrTarryStools: null,
      hemorrhoids: null,
      constipation: null,
      diarrhea: null,
      abdominalPain: null,
      foodIntolerance: null,
      excessiveBelching: null,
      jaundice: null,
      liverGallbladderTrouble: null,
      hepatitis: null,

      // PERIPHERAL VASCULAR
      claudication: null,
      legCramps: null,
      varicoseVeins: null,
      pastClots: null,
      calfLegFeetSwelling: null,
      fingertipColorChangeCold: null,
      swellingRednessTendernessPeripheral: null,

      // URINARY
      frequencyOfUrination: null,
      polyuria: null,
      nocturia: null,
      urgency: null,
      burningPainDuringUrination: null,
      hematuria: null,
      urinaryInfections: null,
      kidneyOrFlankPain: null,
      kidneyStones: null,
      ureteralColic: null,
      suprapubicPain: null,
      incontinence: null,
      reducedUrinaryCaliberOrForce: null,
      hesitancy: null,
      dribbling: null,

      // GENITAL - MALE
      hernias: null,
      penileDischarge: null,
      testicularPainOrMasses: null,
      scrotalPainOrSwelling: null,

      // STD History
      stdHistory: null,
      stdTreatment: null,

      // Sexual Habits
      sexualInterest: null,
      sexualFunction: null,
      sexualSatisfaction: null,
      birthControlMethodUse: null,
      condomUse: null,
      sexualProblems: null,
      hivInfectionConcerns: null,

      // GENITAL - FEMALE
      ageAtMenarche: null,
      periodFrequency: null,
      durationOfPeriods: null,
      amountOfBleeding: null,
      periodRegularity: null,
      bleedingBetweenPeriods: null,
      bleedingAfterIntercourse: null,
      lastMenstrualPeriod: null,
      dysmenorrhea: null,
      premenstrualTension: null,
      ageAtMenopause: null,
      menopausalSymptoms: null,
      postMenopausalBleeding: null,
      vaginalDischarge: null,
      itching_female: null,
      sores_female: null,
      lumps_female: null,
      historyOfSexuallyTransmittedInfections_female: null,
      treatmentOfSexuallyTransmittedInfections_female: null,
      numberOfPregnancies: null,
      numberOfDeliveries: null,
      typeOfDeliveries: null,
      numberOfAbortions: null,
      birthControlMethods: null,
      complicationsOfPregnancy: null,

      // SEXUAL HABITS (FEMALE-SPECIFIC)
      sexualInterest_female: null,
      sexualFunction_female: null,
      sexualSatisfaction_female: null,
      anyProblemsIncludingDyspareunia_female: null,
      concernsAboutHivInfection_female: null,

      // MUSCULOSKELETAL
      muscleOrJointPain: null,
      stiffness: null,
      arthritis: null,
      gout: null,
      backache: null,
      muscleLocationDescription: null,
      swelling_muscle: null,
      redness_muscle: null,
      painMusculoskeletal: null,
      tenderness_muscle: null,
      weaknessMusculoskeletal: null,
      numbnessInLimb: null,
      limitationOfMotionOrActivity: null,
      timingMorning: null,
      timingEvening: null,
      duration: null,
      historyOfTrauma: null,
      neckOrLowBackPain: null,
      systemicJointPain: null,

      // PSYCHIATRIC
      nervousness: null,
      tension: null,
      feelingDownSadDepressed: null,
      memoryChange: null,
      suicidePlansOrAttempts: null,
      suicidalThoughts: null,
      pastCounseling: null,
      psychiatricAdmissions: null,
      onPsychiatricMedications: null,

      // NEUROLOGIC
      changeInMoodNeurologic: null,
      changeInAttention: null,
      changeInSpeech: null,
      changeInOrientation: null,
      changeInMemoryNeurologic: null,
      changeInInsight: null,
      changeInJudgement: null,
      headacheNeurologic: null,
      dizzinessNeurologic: null,
      vertigoNeurologic: null,
      fainting: null,
      blackouts: null,
      weaknessNeurologic: null,
      paralysis: null,
      numbnessOrLossOfSensation: null,
      tinglingPinsAndNeedles: null,
      tremorsOrOtherInvoluntaryMovement: null,
      seizures: null,

      // HEMATOLOGIC
      anemia: null,
      easyBruisingOrBleeding: null,
      pastTransfusions: null,
      transfusionReactions: null,

      // ENDOCRINE
      thyroidTrouble: null,
      heatOrColdIntolerance: null,
      excessiveSweating: null,
      excessiveThirstOrHunger: null,
      polyuriaEndocrine: null,
      changeInGloveOrShoeSize: null,
    },
  });

  // Update form appointmentId when it changes (only if form is not already populated)
  useEffect(() => {
    if (finalAppointmentId && !existingRosForm) {
      form.setValue("appointmentId", finalAppointmentId);
      console.log("📝 Updated form appointmentId:", finalAppointmentId);
    }
  }, [finalAppointmentId, form, existingRosForm]);

  // Fetch existing ROS form data
  useEffect(() => {
    const fetchRosForm = async () => {
      if (!finalAppointmentId) {
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        console.log(
          "🔍 Fetching ROS form for appointmentId:",
          finalAppointmentId,
        );

        const response = await getRosFormByAppointmentId_apiCalls({
          appointmentId: finalAppointmentId,
        });

        console.log("📋 ROS form API response:", response);

        if (response?.data) {
          setExistingRosForm(response.data);
          console.log("✅ ROS form data loaded:", response.data);

          // Populate form with existing data - reset form with all values
          const formData = { ...response.data };

          // Remove fields that shouldn't be in the form
          delete formData.id;
          delete formData.createdAt;
          delete formData.updatedAt;

          // Ensure appointmentId is set
          formData.appointmentId = finalAppointmentId;

          // Reset form with all the data at once
          form.reset(formData);

          console.log("📝 Form populated with existing data:", formData);
        } else {
          console.log("ℹ️ No existing ROS form found for this appointment");
          // Reset form with default values including appointmentId
          form.reset({
            appointmentId: finalAppointmentId,
            // All other fields will use their default values from the form definition
          });
        }
      } catch (error) {
        console.error("❌ Failed to fetch ROS form:", error);
        // Don't show error toast for missing form, it's expected for new forms
      } finally {
        setLoading(false);
      }
    };

    fetchRosForm();
  }, [finalAppointmentId, form]);

  // Form submission handler
  const onSubmit = async (data) => {
    if (!finalAppointmentId) {
      toast({
        title: "Error",
        description: "No appointment ID provided",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);
    try {
      console.log("🚀 Submitting ROS form data:", data);

      let response;
      if (existingRosForm?.id) {
        // Update existing form
        response = await updateRosForm_apiCalls({
          ...data,
          id: existingRosForm.id,
        });
        console.log("✅ ROS form updated successfully:", response);
      } else {
        // Create new form
        response = await createRosForm_apiCalls(data);
        console.log("✅ ROS form created successfully:", response);
      }

      toast({
        title: "Success",
        description: `Review of Systems form has been ${existingRosForm?.id ? "updated" : "created"} successfully.`,
        variant: "default",
      });

      // Update existing form state if it was a create operation
      if (!existingRosForm?.id && response?.data) {
        setExistingRosForm(response.data);
      }
    } catch (error) {
      console.error("❌ Error submitting ROS form:", error);
      toast({
        title: "Error",
        description:
          "There was a problem submitting the form. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderRadioField = (name, label) => {
    return (
      <div className="w-full grid grid-cols-4 gap-4 items-center">
        <FormField
          control={form.control}
          name={name}
          render={({ field }) => (
            <>
              <FormItem className="col-span-2 flex items-center space-y-0">
                <FormLabel
                  id={`${name}-label`}
                  className="text-[16px] font-[600]"
                  title="Click the same option again to unselect"
                >
                  {label}
                </FormLabel>
              </FormItem>

              <FormItem className="col-span-2 flex items-center space-y-0">
                <FormControl>
                  <RadioGroup
                    onValueChange={(value) => {
                      const newValue =
                        value === "yes" ? true : value === "no" ? false : null;
                      if (
                        (value === "yes" && field.value === true) ||
                        (value === "no" && field.value === false)
                      ) {
                        field.onChange(null);
                      } else {
                        field.onChange(newValue);
                      }
                    }}
                    value={
                      field.value === true
                        ? "yes"
                        : field.value === false
                          ? "no"
                          : ""
                    }
                    className="w-full grid grid-cols-1 md:grid-cols-2 gap-2"
                    aria-labelledby={`${name}-label`}
                  >
                    <FormItem className="col-span-1 flex items-center space-x-2 space-y-0 border border-[#E7E8E9] px-8 py-4 rounded-lg hover:bg-gray-50">
                      <FormControl>
                        <RadioGroupItem
                          value="yes"
                          id={`${name}-yes`}
                          onClick={() => {
                            if (field.value === true) {
                              field.onChange(null);
                            }
                          }}
                        />
                      </FormControl>
                      <FormLabel
                        htmlFor={`${name}-yes`}
                        className="text-[16px] font-[600] leading-none cursor-pointer"
                      >
                        Yes
                      </FormLabel>
                    </FormItem>

                    <FormItem className="col-span-1 flex items-center space-x-2 space-y-0 border border-[#E7E8E9] px-4 py-2 rounded-lg hover:bg-gray-50">
                      <FormControl>
                        <RadioGroupItem
                          value="no"
                          id={`${name}-no`}
                          onClick={() => {
                            if (field.value === false) {
                              field.onChange(null);
                            }
                          }}
                        />
                      </FormControl>
                      <FormLabel
                        htmlFor={`${name}-no`}
                        className="text-[16px] font-[600] leading-none cursor-pointer"
                      >
                        No
                      </FormLabel>
                    </FormItem>
                  </RadioGroup>
                </FormControl>
                <FormMessage />
              </FormItem>
            </>
          )}
        />
      </div>
    );
  };
  // Function to create an input field
  const renderInputField = (name, label, placeholder = "Enter value") => {
    return (
      <div className="w-full grid grid-cols-4 gap-4 items-center">
        <FormField
          control={form.control}
          name={name}
          render={({ field }) => (
            <>
              <FormItem className="col-span-2 flex items-center space-y-0">
                <FormLabel className="text-[16px] font-[600] px-2">
                  {label}
                </FormLabel>
              </FormItem>
              <FormItem className="col-span-2">
                <FormControl>
                  <Input id={name} placeholder={placeholder} {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            </>
          )}
        />
      </div>
    );
  };

  // Helper function to render radio fields from array
  const renderRadioFields = (fields) => {
    if (!fields) return null;
    return fields.map((field) => renderRadioField(field.name, field.label));
  };

  // Helper function to render input fields from array
  const renderInputFields = (fields) => {
    if (!fields) return null;
    return fields.map((field) => renderInputField(field.name, field.label, field.placeholder));
  };

  // Helper function to render date fields from array
  const renderDateFields = (fields) => {
    if (!fields) return null;
    return fields.map((field) => renderInputField(field.name, field.label, field.placeholder));
  };

  // Helper function to render all field types for a section
  const renderSectionFields = (sectionConfig) => {
    if (!sectionConfig) return null;
    return (
      <>
        {sectionConfig.radio && renderRadioFields(sectionConfig.radio)}
        {sectionConfig.input && renderInputFields(sectionConfig.input)}
        {sectionConfig.date && renderDateFields(sectionConfig.date)}
      </>
    );
  };

  // Function to create a textarea field
  const renderTextareaField = (name, label, placeholder = "Enter notes") => {
    return (
      <div className="w-full grid grid-cols-4 gap-4 items-center">
        <FormField
          control={form.control}
          name={name}
          render={({ field }) => (
            <>
              <FormItem className="col-span-2 flex items-center space-y-0">
                <FormLabel className="text-[16px] font-[600] px-2">
                  {label}
                </FormLabel>
              </FormItem>
              <FormItem className="col-span-2">
                <FormControl>
                  <Textarea id={name} placeholder={placeholder} {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            </>
          )}
        />
      </div>
    );
  };

  // Loading state
  if (loading) {
    return (
      <div className="flex justify-center items-center py-8">
        <Loader2 className="h-6 w-6 animate-spin mr-2" />
        <span>Loading ROS form...</span>
      </div>
    );
  }

  return (
    <div className="container mx-auto">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="">
          <Accordion type="multiple" className="w-full space-y-4">
            {/* General */}
            <AccordionItem value="general" className="border rounded-lg">
              <AccordionTrigger className="px-4 py-2 hover:bg-gray-50 rounded-t-lg">
                <h3 className="text-xl font-bold">General</h3>
              </AccordionTrigger>
              <AccordionContent className="px-4 py-4 space-y-2">
                <div className="grid grid-cols-1 gap-2">
                  {renderRadioFields(fieldConfigs.general.radio)}
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* Skin */}
            <AccordionItem value="skin" className="border rounded-lg">
              <AccordionTrigger className="px-4 py-2 hover:bg-gray-50 rounded-t-lg">
                <h3 className="text-xl font-bold">Skin</h3>
              </AccordionTrigger>
              <AccordionContent className="px-4 py-4 space-y-2">
                <div className="grid grid-cols-1 gap-2">
                  {renderRadioFields(fieldConfigs.skin.radio)}
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* HEENT */}
            <AccordionItem value="heent-head" className="border rounded-lg">
              <AccordionTrigger className="px-4 py-2 hover:bg-gray-50 rounded-t-lg">
                <h3 className="text-xl font-bold">HEENT</h3>
              </AccordionTrigger>
              <AccordionContent className="">
                <div className="space-y-6 px-4">
                  <div className="space-y-4">
                    <h2 className="text-xl font-semibold my-4">Head</h2>
                    <div className="grid grid-cols-1 gap-2">
                      {renderRadioFields(fieldConfigs.heentHead.radio)}
                    </div>
                  </div>

                  <div>
                    <h2 className="text-xl font-semibold my-4">Eyes</h2>
                    <div className="grid grid-cols-1 gap-2">
                      {renderRadioFields(fieldConfigs.heentEyes.radio)}
                    </div>
                  </div>

                  <div>
                    <h2 className="text-xl font-semibold my-4">Ears</h2>
                    <div className="grid grid-cols-1 gap-2">
                      {renderRadioFields(fieldConfigs.heentEars.radio)}
                    </div>
                  </div>

                  <div>
                    <h2 className="text-xl font-semibold my-4">
                      Nose and Sinuses
                    </h2>
                    <div className="grid grid-cols-1 gap-2">
                      {renderRadioFields(fieldConfigs.heentNose.radio)}
                    </div>
                  </div>

                  <div>
                    <h2 className="text-xl font-semibold mb-4">
                      Throat (Mouth & Pharynx)
                    </h2>
                    <div className="grid grid-cols-1 gap-2">
                      {renderRadioFields(fieldConfigs.heentThroat.radio)}
                      {renderInputFields(fieldConfigs.heentThroat.input)}
                      {renderDateFields(fieldConfigs.heentThroat.date)}
                    </div>
                  </div>
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* Neck */}
            <AccordionItem value="neck" className="border rounded-lg">
              <AccordionTrigger className="px-4 py-2 hover:bg-gray-50 rounded-t-lg">
                <h3 className="text-xl font-bold">Neck</h3>
              </AccordionTrigger>
              <AccordionContent className="px-4 py-4 space-y-2">
                <div className="grid grid-cols-1 gap-2">
                  {renderRadioFields(fieldConfigs.neck.radio)}
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* Breasts */}
            <AccordionItem value="breasts" className="border rounded-lg">
              <AccordionTrigger className="px-4 py-2 hover:bg-gray-50 rounded-t-lg">
                <h3 className="text-xl font-bold">Breasts</h3>
              </AccordionTrigger>
              <AccordionContent className="px-4 py-4 space-y-2">
                <div className="grid grid-cols-1 gap-2">
                  {renderRadioFields(fieldConfigs.breasts.radio)}
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* Respiratory */}
            <AccordionItem value="respiratory" className="border rounded-lg">
              <AccordionTrigger className="px-4 py-2 hover:bg-gray-50 rounded-t-lg">
                <h3 className="text-xl font-bold">Respiratory</h3>
              </AccordionTrigger>
              <AccordionContent className="px-4 py-4 space-y-2">
                <div className="grid grid-cols-1 gap-2">
                  {renderRadioFields(fieldConfigs.respiratory.radio)}

                  <h3 className="text-base font-semibold">
                    If yes, provide sputum details
                  </h3>
                  {renderInputFields(fieldConfigs.respiratory.input)}
                  {renderDateFields(fieldConfigs.respiratory.date)}
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* Cardiovascular */}
            <AccordionItem value="cardiovascular" className="border rounded-lg">
              <AccordionTrigger className="px-4 py-2 hover:bg-gray-50 rounded-t-lg">
                <h3 className="text-xl font-bold">Cardiovascular</h3>
              </AccordionTrigger>
              <AccordionContent className="px-4 py-4 space-y-2">
                <div className="grid grid-cols-1 gap-2">
                  {renderRadioFields(fieldConfigs.cardiovascular.radio)}
                  {renderInputFields(fieldConfigs.cardiovascular.input)}
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* Gastrointestinal */}
            <AccordionItem
              value="gastrointestinal"
              className="border rounded-lg"
            >
              <AccordionTrigger className="px-4 py-2 hover:bg-gray-50 rounded-t-lg">
                <h3 className="text-xl font-bold">Gastrointestinal</h3>
              </AccordionTrigger>
              <AccordionContent className="px-4 py-4 space-y-2">
                <div className="grid grid-cols-1 gap-2">
                  {renderRadioFields(fieldConfigs.gastrointestinal.radio)}

                  <h3 className="text-base font-semibold">Bowel Movements</h3>
                  {renderInputFields(fieldConfigs.gastrointestinal.input)}
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* Peripheral Vascular */}
            <AccordionItem
              value="peripheral-vascular"
              className="border rounded-lg"
            >
              <AccordionTrigger className="px-4 py-2 hover:bg-gray-50 rounded-t-lg">
                <h3 className="text-xl font-bold">Peripheral Vascular</h3>
              </AccordionTrigger>
              <AccordionContent className="px-4 py-4 space-y-2">
                <div className="grid grid-cols-1 gap-2">
                  {renderRadioFields(fieldConfigs.peripheralVascular.radio)}
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* Urinary */}
            <AccordionItem value="urinary" className="border rounded-lg">
              <AccordionTrigger className="px-4 py-2 hover:bg-gray-50 rounded-t-lg">
                <h3 className="text-xl font-bold">Urinary</h3>
              </AccordionTrigger>
              <AccordionContent className="px-4 py-4 space-y-2">
                <div className="grid grid-cols-1 gap-2">
                  {renderRadioFields(fieldConfigs.urinary.radio)}
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* Genital (Male) */}
            <AccordionItem value="genital-male" className="border rounded-lg">
              <AccordionTrigger className="px-4 py-2 hover:bg-gray-50 rounded-t-lg">
                <h3 className="text-xl font-bold">Genital (Male)</h3>
              </AccordionTrigger>
              <AccordionContent className="px-4 py-4 space-y-2">
                <div className="grid grid-cols-1 gap-2">
                  {renderRadioFields(fieldConfigs.genitalMale.radio)}

                  <h3 className="text-base font-semibold">
                    History of Sexually Transmitted infections
                  </h3>
                  {renderRadioFields(fieldConfigs.stdHistory.radio)}
                  {renderInputFields(fieldConfigs.stdHistory.input)}

                  <h3 className="text-base font-semibold">Sexual Habits</h3>
                  {renderRadioFields(fieldConfigs.sexualHabits.radio)}
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* Genital (Female) */}
            <AccordionItem value="genital-female" className="border rounded-lg">
              <AccordionTrigger className="px-4 py-2 hover:bg-gray-50 rounded-t-lg">
                <h3 className="text-xl font-bold">Genital (Female)</h3>
              </AccordionTrigger>
              <AccordionContent className="px-4 py-4 space-y-2">
                <div className="grid grid-cols-1 gap-2">
                  {renderInputFields(fieldConfigs.genitalFemale.input)}
                  {renderDateFields(fieldConfigs.genitalFemale.date)}
                  {renderRadioFields(fieldConfigs.genitalFemale.radio)}

                  <h3 className="text-base font-semibold">Sexual Habits</h3>
                  {renderRadioFields(fieldConfigs.sexualHabitsFemale.radio)}
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* Musculoskeletal */}
            <AccordionItem
              value="musculoskeletal"
              className="border rounded-lg"
            >
              <AccordionTrigger className="px-4 py-2 hover:bg-gray-50 rounded-t-lg">
                <h3 className="text-xl font-bold">Musculoskeletal</h3>
              </AccordionTrigger>
              <AccordionContent className="px-4 py-4 space-y-2">
                <div className="grid grid-cols-1 gap-2">
                  {renderRadioFields(fieldConfigs.musculoskeletal.radio)}
                  {renderInputFields(fieldConfigs.musculoskeletal.input)}
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* Psychiatric  */}
            <AccordionItem value="psychiatric" className="border rounded-lg">
              <AccordionTrigger className="px-4 py-2 hover:bg-gray-50 rounded-t-lg">
                <h3 className="text-xl font-bold">Psychiatric</h3>
              </AccordionTrigger>
              <AccordionContent className="px-4 py-4 space-y-2">
                <div className="grid grid-cols-1 gap-2">
                  {renderRadioFields(fieldConfigs.psychiatric.radio)}
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* Neurologic */}
            <AccordionItem value="neurologic" className="border rounded-lg">
              <AccordionTrigger className="px-4 py-2 hover:bg-gray-50 rounded-t-lg">
                <h3 className="text-xl font-bold">Neurologic</h3>
              </AccordionTrigger>
              <AccordionContent className="px-4 py-4 space-y-2">
                <div className="grid grid-cols-1 gap-2">
                  {renderRadioFields(fieldConfigs.neurologic.radio)}
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* Hematologic */}
            <AccordionItem value="hematologic" className="border rounded-lg">
              <AccordionTrigger className="px-4 py-2 hover:bg-gray-50 rounded-t-lg">
                <h3 className="text-xl font-bold">Hematologic</h3>
              </AccordionTrigger>
              <AccordionContent className="px-4 py-4 space-y-2">
                <div className="grid grid-cols-1 gap-2">
                  {renderRadioFields(fieldConfigs.hematologic.radio)}
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* Endocrine */}
            <AccordionItem value="endocrine" className="border rounded-lg">
              <AccordionTrigger className="px-4 py-2 hover:bg-gray-50 rounded-t-lg">
                <h3 className="text-xl font-bold">Endocrine</h3>
              </AccordionTrigger>
              <AccordionContent className="px-4 py-4 space-y-2">
                <div className="grid grid-cols-1 gap-2">
                  {renderRadioFields(fieldConfigs.endocrine.radio)}
                </div>
              </AccordionContent>
            </AccordionItem>
          </Accordion>

          <div className="flex justify-end gap-4 py-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => form.reset()}
              disabled={isSubmitting}
            >
              Reset
            </Button>
            <Button type="submit" disabled={isSubmitting} variant="primary">
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  {existingRosForm?.id ? "Updating..." : "Creating..."}
                </>
              ) : existingRosForm?.id ? (
                "Update ROS Form"
              ) : (
                "Create ROS Form"
              )}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
};
export default ROS;
