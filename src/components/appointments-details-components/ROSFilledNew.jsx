import { useState, useEffect } from "react";
import { useLocation } from "react-router-dom";
import { Loader2 } from "lucide-react";
import { getRosFormByAppointmentId_apiCalls } from "../../api/api_calls/appointmentforms/rosForm_apiCalls";
import { fieldConfigs } from "../../schemas/rosFieldConfigs";

const ROSFilled = ({ appointmentId }) => {
  const [loading, setLoading] = useState(true);
  const [rosData, setRosData] = useState(null);
  const location = useLocation();

  // Get appointmentId from props or location state as fallback
  const finalAppointmentId = appointmentId ||
    location.state?.appointmentId ||
    location.state?.appointment?.id;

  // Fetch ROS form data
  useEffect(() => {
    const fetchRosForm = async () => {
      if (!finalAppointmentId) {
        console.log("No appointment ID provided");
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        console.log("Fetching ROS form for appointmentId:", finalAppointmentId);

        const response = await getRosFormByAppointmentId_apiCalls({
          appointmentId: finalAppointmentId,
        });

        console.log("ROS form API response:", response);

        if (response?.data) {
          setRosData(response.data);
          console.log("ROS form data loaded:", response.data);
        } else {
          console.log("No ROS form found for this appointment");
        }
      } catch (error) {
        console.error("Failed to fetch ROS form:", error);
        // Don't set error state, just log it
      } finally {
        setLoading(false);
      }
    };

    fetchRosForm();
  }, [finalAppointmentId]);

  // Helper function to format boolean values
  const formatBoolean = (value) => {
    if (value === true) return "Yes";
    if (value === false) return "No";
    return "N/A";
  };

  // Helper function to format string values
  const formatString = (value) => {
    return value && value.trim() !== "" ? value : "N/A";
  };

  // Helper function to format date values
  const formatDate = (value) => {
    if (!value) return "N/A";
    try {
      const date = new Date(value);
      return date.toLocaleDateString();
    } catch {
      return "N/A";
    }
  };

  // Helper function to render a field value based on its type
  const renderFieldValue = (fieldName, fieldType = "radio") => {
    const value = rosData[fieldName];
    
    switch (fieldType) {
      case "radio":
        return formatBoolean(value);
      case "input":
        return formatString(value);
      case "date":
        return formatDate(value);
      default:
        return formatString(value);
    }
  };

  // Helper function to render fields from configuration
  const renderFields = (fields, fieldType = "radio") => {
    if (!fields) return null;
    
    return fields.map((field) => (
      <div key={field.name} className="space-y-1">
        <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
          {field.label}
        </h3>
        <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
          {renderFieldValue(field.name, fieldType)}
        </p>
      </div>
    ));
  };

  // Helper function to render a complete section
  const renderSection = (sectionTitle, sectionConfig) => {
    if (!sectionConfig) return null;

    return (
      <div className="w-full border border-[#E7E8E9] rounded-lg p-4 md:p-6">
        <h2 className="text-lg md:text-xl font-semibold text-[#1E1E1E] mb-4 md:mb-6">
          {sectionTitle}
        </h2>
        
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 gap-4 md:gap-6">
          {sectionConfig.radio && renderFields(sectionConfig.radio, "radio")}
          {sectionConfig.input && renderFields(sectionConfig.input, "input")}
          {sectionConfig.date && renderFields(sectionConfig.date, "date")}
        </div>
      </div>
    );
  };

  // Helper function to render HEENT section with subsections
  const renderHEENTSection = () => {
    return (
      <div className="w-full border border-[#E7E8E9] rounded-lg p-4 md:p-6">
        <h2 className="text-lg md:text-xl font-semibold text-[#1E1E1E] mb-4 md:mb-6">
          Head, Eyes, Ears, Nose, Throat (HEENT)
        </h2>

        {/* Head */}
        <h3 className="text-lg md:text-xl font-semibold text-[#1E1E1E] mt-4">Head</h3>
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 gap-4 md:gap-6 border-solid border-b-[1px] py-4 border-b-[#E7E8E9]">
          {renderFields(fieldConfigs.heentHead.radio, "radio")}
        </div>

        {/* Eyes */}
        <h3 className="text-lg md:text-xl font-semibold text-[#1E1E1E] mt-4">Eyes</h3>
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 gap-4 md:gap-6 border-solid border-b-[1px] py-4 border-b-[#E7E8E9]">
          {renderFields(fieldConfigs.heentEyes.radio, "radio")}
        </div>

        {/* Ears */}
        <h3 className="text-lg md:text-xl font-semibold text-[#1E1E1E] mt-4">Ears</h3>
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 gap-4 md:gap-6 border-solid border-b-[1px] py-4 border-b-[#E7E8E9]">
          {renderFields(fieldConfigs.heentEars.radio, "radio")}
        </div>

        {/* Nose and Sinuses */}
        <h3 className="text-lg md:text-xl font-semibold text-[#1E1E1E] mt-4">Nose and Sinuses</h3>
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 gap-4 md:gap-6 border-solid border-b-[1px] py-4 border-b-[#E7E8E9]">
          {renderFields(fieldConfigs.heentNose.radio, "radio")}
        </div>

        {/* Throat */}
        <h3 className="text-lg md:text-xl font-semibold text-[#1E1E1E] mt-4">Throat (Mouth & Pharynx)</h3>
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 gap-4 md:gap-6 py-4">
          {renderFields(fieldConfigs.heentThroat.radio, "radio")}
          {renderFields(fieldConfigs.heentThroat.input, "input")}
          {renderFields(fieldConfigs.heentThroat.date, "date")}
        </div>
      </div>
    );
  };

  // Loading state
  if (loading) {
    return (
      <div className="flex justify-center items-center py-8">
        <Loader2 className="h-6 w-6 animate-spin mr-2" />
        <span>Loading ROS form...</span>
      </div>
    );
  }

  // No data state
  if (!rosData) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">No Review of Systems form found for this appointment.</p>
      </div>
    );
  }

  return (
    <div className="flex justify-center w-full ">
      <div className="w-full space-y-6">
        {/* General */}
        {renderSection("General", fieldConfigs.general)}

        {/* Skin */}
        {renderSection("Skin", fieldConfigs.skin)}

        {/* HEENT */}
        {renderHEENTSection()}

        {/* Neck */}
        {renderSection("Neck", fieldConfigs.neck)}

        {/* Breasts */}
        {renderSection("Breasts", fieldConfigs.breasts)}

        {/* Respiratory */}
        {renderSection("Respiratory", fieldConfigs.respiratory)}

        {/* Cardiovascular */}
        {renderSection("Cardiovascular", fieldConfigs.cardiovascular)}

        {/* Gastrointestinal */}
        {renderSection("Gastrointestinal", fieldConfigs.gastrointestinal)}

        {/* Peripheral Vascular */}
        {renderSection("Peripheral Vascular", fieldConfigs.peripheralVascular)}

        {/* Urinary */}
        {renderSection("Urinary", fieldConfigs.urinary)}

        {/* Genital (Male) */}
        {renderSection("Genital (Male)", fieldConfigs.genitalMale)}

        {/* STD History */}
        {renderSection("STD History", fieldConfigs.stdHistory)}

        {/* Sexual Habits */}
        {renderSection("Sexual Habits", fieldConfigs.sexualHabits)}

        {/* Genital (Female) */}
        {renderSection("Genital (Female)", fieldConfigs.genitalFemale)}

        {/* Sexual Habits (Female) */}
        {renderSection("Sexual Habits (Female)", fieldConfigs.sexualHabitsFemale)}

        {/* Musculoskeletal */}
        {renderSection("Musculoskeletal", fieldConfigs.musculoskeletal)}

        {/* Psychiatric */}
        {renderSection("Psychiatric", fieldConfigs.psychiatric)}

        {/* Neurologic */}
        {renderSection("Neurologic", fieldConfigs.neurologic)}

        {/* Hematologic */}
        {renderSection("Hematologic", fieldConfigs.hematologic)}

        {/* Endocrine */}
        {renderSection("Endocrine", fieldConfigs.endocrine)}
      </div>
    </div>
  );
};

export default ROSFilled;
