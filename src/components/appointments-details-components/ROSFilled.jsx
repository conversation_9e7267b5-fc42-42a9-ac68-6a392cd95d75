import { useState, useEffect } from "react";
import { useLocation } from "react-router-dom";
import { Loader2 } from "lucide-react";
import { getRosFormByAppointmentId_apiCalls } from "../../api/api_calls/appointmentforms/rosForm_apiCalls";

// Field configurations for each section based on complete schema
const fieldConfigs = {
  general: {
    radio: [
      { name: "recentWeightChange", label: "Recent Weight Change" },
      { name: "weakness", label: "Weakness" },
      { name: "fatigue", label: "Fatigue" },
      { name: "fever", label: "Fever" },
    ]
  },
  skin: {
    radio: [
      { name: "skinRashes", label: "Rashes" },
      { name: "skinLumps", label: "Lumps" },
      { name: "skinSores", label: "Sores" },
      { name: "skinItching", label: "Itching" },
      { name: "skinDryness", label: "Dryness" },
      { name: "skinChangeInColor", label: "Change in Color" },
      { name: "skinChangeInHair", label: "Change in Hair" },
      { name: "skinChangeInMoles", label: "Change in Moles" },
    ]
  },
  heentHead: {
    radio: [
      { name: "headache", label: "Headache" },
      { name: "headInjury", label: "Head Injury" },
      { name: "dizziness", label: "Dizziness" },
      { name: "lightheadedness", label: "Lightheadedness" },
    ]
  },
  heentEyes: {
    radio: [
      { name: "visionChange", label: "Vision Change" },
      { name: "glassesOrContactLenses", label: "Glasses or Contact Lenses" },
      { name: "eyePain", label: "Eye Pain" },
      { name: "eyeRedness", label: "Eye Redness" },
      { name: "excessiveTearing", label: "Excessive Tearing" },
      { name: "doubleOrBlurredVision", label: "Double or Blurred Vision" },
      { name: "spots", label: "Spots" },
      { name: "specks", label: "Specks" },
      { name: "flashingLights", label: "Flashing Lights" },
      { name: "glaucoma", label: "Glaucoma" },
      { name: "cataracts", label: "Cataracts" },
    ]
  },
  heentEars: {
    radio: [
      { name: "hearingLoss", label: "Hearing Loss" },
      { name: "tinnitus", label: "Ringing in Ear(s)/Tinnitus" },
      { name: "vertigo", label: "Vertigo" },
      { name: "earaches", label: "Earaches" },
      { name: "earInfection", label: "Infection" },
      { name: "earDischarge", label: "Discharge" },
      { name: "hearingDecreased", label: "Hearing Decreased" },
      { name: "hearingAidsUse", label: "Use/non-use of hearing aids" },
    ]
  },
  heentNose: {
    radio: [
      { name: "nasalDischarge", label: "Discharge" },
      { name: "nasalItching", label: "Itching" },
      { name: "frequentColds", label: "Frequent Colds" },
      { name: "hayfever", label: "Hayfever" },
      { name: "nasalStuffiness", label: "Nasal Stuffiness" },
      { name: "nosebleeds", label: "Nosebleeds" },
      { name: "sinusPressurePain", label: "Sinus pressure/pain" },
    ]
  },
  heentThroat: {
    radio: [
      { name: "dentalCondition", label: "Condition of teeth and gum" },
      { name: "gumProblemsBleeding", label: "Gum Problems/ Bleeding" },
      { name: "dentures", label: "Dentures (if any)" },
      { name: "soreTongue", label: "Sore Tongue" },
      { name: "dryMouth", label: "Dry Mouth" },
      { name: "frequentSoreThroats", label: "Frequent Sore Throats" },
      { name: "hoarseness", label: "Hoarseness" },
    ],
    input: [
      { name: "denturesFit", label: "Fit of Dentures", placeholder: "Describe fit of dentures", type: "text" },
    ],
    date: [
      { name: "lastDentalExam", label: "Last Dental Exam", placeholder: "Enter date of last dental exam", type: "date" },
    ]
  },
  neck: {
    radio: [
      { name: "swollenGlands", label: "Swollen Glands" },
      { name: "thyroidProblems", label: "Thyroid Problems" },
      { name: "goiter", label: "Goiter" },
      { name: "neckLumps", label: "Lumps" },
      { name: "neckPainStiffness", label: "Pain or Stiffness" },
    ]
  },
  breasts: {
    radio: [
      { name: "breastLumps", label: "Lumps" },
      { name: "breastPainDiscomfort", label: "Pain or discomfort" },
      { name: "nippleDischarge", label: "Nipple discharge" },
      { name: "selfExamPractices", label: "Self-exam practices" },
    ]
  },
  respiratory: {
    radio: [
      { name: "cough", label: "Cough" },
      { name: "sputum", label: "Sputum" },
      { name: "sputumBlood", label: "Presence of blood" },
      { name: "shortnessOfBreath_respiratory", label: "Shortness of breath (Dyspnea)" },
      { name: "wheezing", label: "Wheezing" },
      { name: "pleuriticPain", label: "Pain with a deep breath (Pleuritic pain)" },
      { name: "asthma", label: "Asthma" },
      { name: "bronchitis", label: "Bronchitis" },
      { name: "emphysema", label: "Emphysema" },
      { name: "pneumonia", label: "Pneumonia" },
      { name: "tuberculosis", label: "Tuberculosis" },
    ],
    input: [
      { name: "sputumColor", label: "Sputum Color", placeholder: "Describe sputum color", type: "text" },
      { name: "sputumQuantity", label: "Sputum Quantity", placeholder: "Describe sputum quantity", type: "text" },
    ],
    date: [
      { name: "lastChestXray", label: "Last Chest X-Ray", placeholder: "Enter date of last chest X-ray", type: "date" },
    ]
  },
  cardiovascular: {
    radio: [
      { name: "heartTrouble", label: "Heart Trouble" },
      { name: "highBloodPressure", label: "High Blood Pressure" },
      { name: "rheumaticFever", label: "Rheumatic Fever" },
      { name: "heartMurmurs", label: "Heart Murmurs" },
      { name: "chestPain", label: "Chest pain or discomfort" },
      { name: "palpitations", label: "Palpitations" },
      { name: "shortnessOfBreath_cardio", label: "Shortness of Breath" },
      { name: "orthopnea", label: "Use of pillows or head of bed elevation at night to ease breathing (Orthopnea)" },
      { name: "paroxysmalNocturnalDyspnea", label: "Need to sit up at night to ease breathing (paroxysmal nocturnal dyspnea)" },
      { name: "edema", label: "Swelling in the hands, ankles or feet (edema)" },
    ],
    input: [
      { name: "ekgOther", label: "Results of past EKG or other Cardiovascular tests EKG/ Other:", placeholder: "Value", type: "text" },
    ]
  },
  gastrointestinal: {
    radio: [
      { name: "unintentionalWeightChange", label: "Unintentional Weight loss/ gain" },
      { name: "troubleSwallowing", label: "Trouble swallowing" },
      { name: "heartburn", label: "Heartburn" },
      { name: "appetite", label: "Appetite" },
      { name: "specialDiet", label: "On Special Diet" },
      { name: "nausea", label: "Nausea" },
      { name: "changeInBowelHabits", label: "Change in Bowel Habits" },
      { name: "painWithDefecation", label: "Pain with Defecation" },
      { name: "rectalBleeding", label: "Rectal Bleeding" },
      { name: "blackOrTarryStools", label: "Black or Tarry Stools" },
      { name: "hemorrhoids", label: "Hemorrhoids" },
      { name: "constipation", label: "Constipation" },
      { name: "diarrhea", label: "Diarrhea/Irritable Bowel Syndrome" },
      { name: "abdominalPain", label: "Abdominal Pain" },
      { name: "foodIntolerance", label: "Food Intolerance" },
      { name: "excessiveBelching", label: "Excessive belching or passing of gas" },
      { name: "jaundice", label: "Jaundice (yellowing of skin)" },
      { name: "liverGallbladderTrouble", label: "Liver or Gallbladder Trouble" },
      { name: "hepatitis", label: "Hepatitis" },
    ],
    input: [
      { name: "stoolColor", label: "Stool Color", placeholder: "Describe stool color", type: "text" },
      { name: "stoolSize", label: "Stool Size", placeholder: "Describe stool size", type: "text" },
    ]
  },
  peripheralVascular: {
    radio: [
      { name: "claudication", label: "Intermittent leg pain with exertion (claudication)" },
      { name: "legCramps", label: "Leg Cramps" },
      { name: "varicoseVeins", label: "Varicose Veins" },
      { name: "pastClots", label: "Past clots in the veins" },
      { name: "calfLegFeetSwelling", label: "Swelling in the calves, legs, or feet" },
      { name: "fingertipColorChangeCold", label: "Color change in fingertips/toes during cold weather" },
      { name: "swellingRednessTendernessPeripheral", label: "Swelling with redness/tenderness" },
    ]
  },
  urinary: {
    radio: [
      { name: "frequencyOfUrination", label: "Frequency of Urination" },
      { name: "polyuria", label: "Polyuria" },
      { name: "nocturia", label: "Nocturia" },
      { name: "urgency", label: "Urgency" },
      { name: "burningPainDuringUrination", label: "Burning or Pain During Urination" },
      { name: "hematuria", label: "Hematuria" },
      { name: "urinaryInfections", label: "Urinary infections" },
      { name: "kidneyOrFlankPain", label: "Kidney or Flank Pain" },
      { name: "kidneyStones", label: "Kidney Stones" },
      { name: "ureteralColic", label: "Ureteral Colic" },
      { name: "suprapubicPain", label: "Suprapubic Pain" },
      { name: "incontinence", label: "Incontinence" },
      { name: "reducedUrinaryCaliberOrForce", label: "Reduced caliber or force of the urinary system" },
      { name: "hesitancy", label: "Hesitancy" },
      { name: "dribbling", label: "Dribbling" },
    ]
  },
  genitalMale: {
    radio: [
      { name: "hernias", label: "Hernias" },
      { name: "penileDischarge", label: "Discharge from sores on the penis" },
      { name: "testicularPainOrMasses", label: "Testicular Pain or Masses" },
      { name: "scrotalPainOrSwelling", label: "Scrotal Pain or Swelling" },
    ]
  },
  stdHistory: {
    radio: [
      { name: "stdHistory", label: "History of Sexually Transmitted infections" },
    ],
    input: [
      { name: "stdTreatment", label: "Treatment", placeholder: "Describe STD treatment", type: "text" },
    ]
  },
  sexualHabits: {
    radio: [
      { name: "sexualInterest", label: "Interest" },
      { name: "sexualFunction", label: "Function" },
      { name: "sexualSatisfaction", label: "Satisfaction" },
      { name: "birthControlMethodUse", label: "Birth Control Methods" },
      { name: "condomUse", label: "Condom Use" },
      { name: "sexualProblems", label: "Problems" },
      { name: "hivInfectionConcerns", label: "Concerns about HIV Infection" },
    ]
  },
  genitalFemale: {
    radio: [
      { name: "periodRegularity", label: "Period Regularity" },
      { name: "bleedingBetweenPeriods", label: "Bleeding Between Periods" },
      { name: "bleedingAfterIntercourse", label: "Bleeding After Intercourse" },
      { name: "dysmenorrhea", label: "Dysmenorrhea" },
      { name: "premenstrualTension", label: "Premenstrual Tension" },
      { name: "menopausalSymptoms", label: "Menopausal Symptoms" },
      { name: "postMenopausalBleeding", label: "Post-Menopausal Bleeding" },
      { name: "vaginalDischarge", label: "Vaginal Discharge" },
      { name: "itching_female", label: "Itching" },
      { name: "sores_female", label: "Sores" },
      { name: "lumps_female", label: "Lumps" },
      { name: "historyOfSexuallyTransmittedInfections_female", label: "History of Sexually Transmitted infections" },
      { name: "complicationsOfPregnancy", label: "Complications of pregnancy" },
    ],
    input: [
      { name: "ageAtMenarche", label: "Age at Menarche", placeholder: "Enter age", type: "text" },
      { name: "periodFrequency", label: "Period Frequency", placeholder: "Enter frequency", type: "text" },
      { name: "durationOfPeriods", label: "Duration of Periods", placeholder: "Enter duration", type: "text" },
      { name: "amountOfBleeding", label: "Amount of Bleeding", placeholder: "Describe amount", type: "text" },
      { name: "ageAtMenopause", label: "Age at Menopause", placeholder: "Enter age", type: "text" },
      { name: "treatmentOfSexuallyTransmittedInfections_female", label: "Treatment", placeholder: "value", type: "text" },
      { name: "numberOfPregnancies", label: "Number of pregnancies", placeholder: "value", type: "text" },
      { name: "numberOfDeliveries", label: "Number of deliveries", placeholder: "value", type: "text" },
      { name: "typeOfDeliveries", label: "Type of deliveries", placeholder: "value", type: "text" },
      { name: "numberOfAbortions", label: "Number of Abortions", placeholder: "value", type: "text" },
      { name: "birthControlMethods", label: "Birth Control Methods", placeholder: "value", type: "text" },
    ],
    date: [
      { name: "lastMenstrualPeriod", label: "Last Menstrual Period", placeholder: "Enter date", type: "date" },
    ]
  },
  sexualHabitsFemale: {
    radio: [
      { name: "sexualInterest_female", label: "Interest" },
      { name: "sexualFunction_female", label: "Function" },
      { name: "sexualSatisfaction_female", label: "Satisfaction" },
      { name: "anyProblemsIncludingDyspareunia_female", label: "Any problems including dyspareunia" },
      { name: "concernsAboutHivInfection_female", label: "Concerns about HIV infection" },
    ]
  },
  musculoskeletal: {
    radio: [
      { name: "muscleOrJointPain", label: "Muscle or joint pain" },
      { name: "stiffness", label: "Stiffness" },
      { name: "arthritis", label: "Arthritis" },
      { name: "gout", label: "Gout" },
      { name: "backache", label: "Backache" },
      { name: "swelling_muscle", label: "Swelling" },
      { name: "redness_muscle", label: "Redness" },
      { name: "painMusculoskeletal", label: "Pain" },
      { name: "tenderness_muscle", label: "Tenderness" },
      { name: "weaknessMusculoskeletal", label: "Weakness" },
      { name: "numbnessInLimb", label: "Numbness in Limb" },
      { name: "limitationOfMotionOrActivity", label: "Limitation of Motion or Activity" },
      { name: "timingMorning", label: "Morning" },
      { name: "timingEvening", label: "Evening" },
      { name: "historyOfTrauma", label: "History of Trauma" },
      { name: "neckOrLowBackPain", label: "Neck or Low Back Pain" },
      { name: "systemicJointPain", label: "Joint pain with systemic symptoms such as fever, chills, rash, anorexia, weight loss or weakness" },
    ],
    input: [
      { name: "muscleLocationDescription", label: "If present, describe location of affected joints/muscles:", placeholder: "Describe affected areas", type: "text" },
      { name: "duration", label: "Duration", placeholder: "Enter duration", type: "text" },
    ]
  },
  psychiatric: {
    radio: [
      { name: "nervousness", label: "Nervousness" },
      { name: "tension", label: "Tension" },
      { name: "feelingDownSadDepressed", label: "Feeling Down, Sad, or Depressed" },
      { name: "memoryChange", label: "Memory Change" },
      { name: "suicidePlansOrAttempts", label: "Suicide Plans or Attempts" },
      { name: "suicidalThoughts", label: "Suicidal Thoughts" },
      { name: "pastCounseling", label: "Past Counseling" },
      { name: "psychiatricAdmissions", label: "Psychiatric Admissions" },
      { name: "onPsychiatricMedications", label: "On Psychiatric Medications" },
    ]
  },
  neurologic: {
    radio: [
      { name: "changeInMoodNeurologic", label: "Change in Mood" },
      { name: "changeInAttention", label: "Change in Attention" },
      { name: "changeInSpeech", label: "Change in Speech" },
      { name: "changeInOrientation", label: "Change in Orientation" },
      { name: "changeInMemoryNeurologic", label: "Change in Memory" },
      { name: "changeInInsight", label: "Change in Insight" },
      { name: "changeInJudgement", label: "Change in Judgement" },
      { name: "headacheNeurologic", label: "Headache" },
      { name: "dizzinessNeurologic", label: "Dizziness" },
      { name: "vertigoNeurologic", label: "Vertigo" },
      { name: "fainting", label: "Fainting" },
      { name: "blackouts", label: "Blackouts" },
      { name: "weaknessNeurologic", label: "Weakness" },
      { name: "paralysis", label: "Paralysis" },
      { name: "numbnessOrLossOfSensation", label: "Numbness or Loss of Sensation" },
      { name: "tinglingPinsAndNeedles", label: "Tingling or Pins and Needles" },
      { name: "tremorsOrOtherInvoluntaryMovement", label: "Tremors or Other involuntary movement" },
      { name: "seizures", label: "Seizures" },
    ]
  },
  hematologic: {
    radio: [
      { name: "anemia", label: "Anemia" },
      { name: "easyBruisingOrBleeding", label: "Easy bruising or bleeding" },
      { name: "pastTransfusions", label: "Past transfusions" },
      { name: "transfusionReactions", label: "Transfusion reactions" },
    ]
  },
  endocrine: {
    radio: [
      { name: "thyroidTrouble", label: "Thyroid trouble" },
      { name: "heatOrColdIntolerance", label: "Heat or Cold Intolerance" },
      { name: "excessiveSweating", label: "Excessive Sweating" },
      { name: "excessiveThirstOrHunger", label: "Excessive thirst or hunger" },
      { name: "polyuriaEndocrine", label: "Polyuria" },
      { name: "changeInGloveOrShoeSize", label: "Change in glove or shoe size" },
    ]
  },
};

const ROSFilled = ({ appointmentId }) => {
  const [loading, setLoading] = useState(true);
  const [rosData, setRosData] = useState(null);
  const location = useLocation();

  // Get appointmentId from props or location state as fallback
  const finalAppointmentId = appointmentId ||
    location.state?.appointmentId ||
    location.state?.appointment?.id;

  // Fetch ROS form data
  useEffect(() => {
    const fetchRosForm = async () => {
      if (!finalAppointmentId) {
        console.log("No appointment ID provided");
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        console.log("Fetching ROS form for appointmentId:", finalAppointmentId);

        const response = await getRosFormByAppointmentId_apiCalls({
          appointmentId: finalAppointmentId,
        });

        console.log("ROS form API response:", response);

        if (response?.data) {
          setRosData(response.data);
          console.log("ROS form data loaded:", response.data);
        } else {
          console.log("No ROS form found for this appointment");
        }
      } catch (error) {
        console.error("Failed to fetch ROS form:", error);
        // Don't set error state, just log it
      } finally {
        setLoading(false);
      }
    };

    fetchRosForm();
  }, [finalAppointmentId]);

  // Helper function to format boolean values
  const formatBoolean = (value) => {
    if (value === true) return "Yes";
    if (value === false) return "No";
    return "N/A";
  };

  // Helper function to format string values
  const formatString = (value) => {
    return value && value.trim() !== "" ? value : "N/A";
  };

  // Loading state
  if (loading) {
    return (
      <div className="flex justify-center items-center py-8">
        <Loader2 className="h-6 w-6 animate-spin mr-2" />
        <span>Loading ROS form...</span>
      </div>
    );
  }

  // No data state
  if (!rosData) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">No Review of Systems form found for this appointment.</p>
      </div>
    );
  }

  return (
    <div className="flex justify-center w-full ">
      <div className="w-full space-y-6">
        {/* General */}
        <div className="w-full border border-[#E7E8E9] rounded-lg p-4 md:p-6">
          <h2 className="text-lg md:text-xl font-semibold text-[#1E1E1E] mb-4 md:mb-6">
            General
          </h2>

          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 gap-4 md:gap-6">
            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Recent weight change
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                {formatBoolean(rosData.recentWeightChange)}
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Weakness
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                {formatBoolean(rosData.weakness)}
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Fatigue
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                {formatBoolean(rosData.fatigue)}
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Fever
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                {formatBoolean(rosData.fever)}
              </p>
            </div>
          </div>
        </div>

        {/* Head, Eyes, Ears, Nose , Throat  (HEENT) */}
        <div className="w-full border border-[#E7E8E9] rounded-lg p-4 md:p-6">
          <h2 className="text-lg md:text-xl font-semibold text-[#1E1E1E] mb-4 md:mb-6">
            Head, Eyes, Ears, Nose , Throat (HEENT)
          </h2>

          <h2 className="text-lg md:text-xl font-semibold text-[#1E1E1E] mt-4">
            Head
          </h2>

          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 gap-4 md:gap-6 border-solid border-b-[1px] py-4 border-b-[#E7E8E9]">
            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Head injury
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                {formatBoolean(rosData.headInjury)}
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Lightheadedness
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                {formatBoolean(rosData.lightheadedness)}
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Dizziness
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                {formatBoolean(rosData.dizziness)}
              </p>
            </div>
          </div>

          <h2 className="text-lg md:text-xl font-semibold text-[#1E1E1E] mt-4">
            Eyes
          </h2>

          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 gap-4 md:gap-6 border-solid border-b-[1px] py-4 border-b-[#E7E8E9]">
            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Vision change
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Glasses or contact lenses
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Pain
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Redness
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Excessive tearing
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Spots
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Double or blurred vision
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Specks
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Glaucoma
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Flashing lights
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Cataracts
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>
          </div>

          <h2 className="text-lg md:text-xl font-semibold text-[#1E1E1E] mt-4">
            Ears
          </h2>

          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 gap-4 md:gap-6 border-solid border-b-[1px] py-4 border-b-[#E7E8E9]">
            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Hearing loss
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Ringing in Ear(s)/Tinnitus
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Vertigo
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Earaches
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Infection
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Discharge
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                If hearing is decreased…
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Use /non‐ use of hearing aids{" "}
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>
          </div>

          <h2 className="text-lg md:text-xl font-semibold text-[#1E1E1E] mt-4">
            Nose and Sinuses
          </h2>

          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 gap-4 md:gap-6 py-4 border-solid border-b-[1px] border-b-[#E7E8E9]">
            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Frequent colds
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Nasal stuffiness
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Discharge
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Itching
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Hayfever
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Nosebleeds
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Sinus pressure/pain
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>
          </div>

          <h2 className="text-lg md:text-xl font-semibold text-[#1E1E1E] mt-4">
            Throat (Mouth & Pharynx)
          </h2>

          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 gap-4 md:gap-6 py-4 ">
            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Condition of teeth and gums
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Gum problems/ Bleeding
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Dentures(if any)
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>
          </div>

          <h2 className="text-md md:text-lg font-semibold text-[#1E1E1E] mt-4">
            Fit of dentures (if any)
          </h2>

          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 gap-4 md:gap-6 py-4">
            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Sore tongue
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Dry mouth
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Hoarseness
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Frequent sore throats
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>
          </div>
        </div>

        {/* {Neck} */}
        <div className="w-full border border-[#E7E8E9] rounded-lg p-4 md:p-6">
          <h2 className="text-lg md:text-xl font-semibold text-[#1E1E1E] mb-4 md:mb-6">
            Neck
          </h2>

          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 gap-4 md:gap-6 py-4">
            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Swollen glands
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Thyroid problems
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Goiter
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Lumps
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Pain or stiffness
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>
          </div>
        </div>

        {/* {Breasts} */}
        <div className="w-full border border-[#E7E8E9] rounded-lg p-4 md:p-6">
          <h2 className="text-lg md:text-xl font-semibold text-[#1E1E1E] mb-4 md:mb-6">
            Breasts
          </h2>

          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 gap-4 md:gap-6  py-4 ">
            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Nipple discharge
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Pain or discomfort
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Lumps
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Self‐examination practices
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>
          </div>
        </div>

        {/* {Respiratory } */}
        <div className="w-full border border-[#E7E8E9] rounded-lg p-4 md:p-6">
          <h2 className="text-lg md:text-xl font-semibold text-[#1E1E1E] mb-4 md:mb-6">
            Respiratory
          </h2>

          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 gap-4 md:gap-6 border-solid ">
            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Cough
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Sputum
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Lumps
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>
          </div>

          <h2 className="text-md md:text-lg font-semibold text-[#1E1E1E] my-4 md:mb-6">
            Sputum Details:
          </h2>

          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 gap-4 md:gap-6 border-solid ">
            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Color:
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Value
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Quantity:
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Value
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Presence of blood
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Shortness of breath (Dyspnea)
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Wheezing
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Pain with a deep breath (Pleuritic pain)
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Asthma
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Bronchitis
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Emphysema
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Pneumonia
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Tuberculosis
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>
          </div>
        </div>

        {/* {Gastrointestinal } */}
        <div className="w-full border border-[#E7E8E9] rounded-lg p-4 md:p-6">
          <h2 className="text-lg md:text-xl font-semibold text-[#1E1E1E] mb-4 md:mb-6">
            Gastrointestinal
          </h2>

          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 gap-4 md:gap-6 border-solid ">
            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Unintentional Weight loss/ gain
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Trouble swallowing
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Heartburn
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Appetite
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Trouble swallowing
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                On Special Diet
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Nausea
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>
          </div>

          <h2 className="text-md md:text-lg font-semibold text-[#1E1E1E] my-4 md:mb-6">
            Bowl Movement:
          </h2>

          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 gap-4 md:gap-6 border-solid ">
            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Stool Color:
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Value
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Size:
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Value
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Presence of blood
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Change in bowel habits
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Pain with Defecation
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Rectal bleeding
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Black or tarry stools
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Hemorrhoids
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Constipation
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Diarrhea / Irritable Bowel Syndrome
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Abdominal pain
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Food intolerance
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>
            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Excessive belching or passing of gas
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>
            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Jaundice (yellowing of skin)
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>
            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Hepatitis
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>
            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Liver or gallbladder trouble
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>
          </div>
        </div>

        {/* { Peripheral Vascular} */}
        <div className="w-full border border-[#E7E8E9] rounded-lg p-4 md:p-6">
          <h2 className="text-lg md:text-xl font-semibold text-[#1E1E1E] mb-4 md:mb-6">
            Peripheral Vascular
          </h2>

          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 gap-4 md:gap-6 border-solid ">
            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Intermittent leg pain with exertion (claudication)
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Leg cramps
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Varicose Veins
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Past clots in the veins
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Swelling in the calves, legs, or feet
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Color change in fingertips/toes during cold weather
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Swelling with redness/tenderness
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>
          </div>
        </div>

        {/* {Urinary} */}
        <div className="w-full border border-[#E7E8E9] rounded-lg p-4 md:p-6">
          <h2 className="text-lg md:text-xl font-semibold text-[#1E1E1E] mb-4 md:mb-6">
            Urinary
          </h2>

          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 gap-4 md:gap-6 border-solid ">
            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Frequency of Urination
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Polyuria
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Nocturia
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Urgency
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Burning or pain during urination
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Urinary infections
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Blood in the urine (hematuria)
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Kidney or flank pain
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Kidney stones
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Ureteral colic
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Suprapubic pain
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Incontinence
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Reduced caliber or force of the urinary system
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Hesitancy
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Dribbling
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>
          </div>
        </div>

        {/* {Genital (Male)} */}
        <div className="w-full border border-[#E7E8E9] rounded-lg p-4 md:p-6">
          <h2 className="text-lg md:text-xl font-semibold text-[#1E1E1E] mb-4 md:mb-6">
            Genital (Male)
          </h2>

          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-2 gap-4 md:gap-6 border-solid ">
            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Hernias
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Discharge from sores on the penis
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Testicular pain or masses
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Scrotal pain or swelling
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>
          </div>

          <h2 className="text-lg md:text-xl font-semibold text-[#1E1E1E] my-4 md:mb-6">
            History of Sexually Transmitted infections
          </h2>

          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 gap-4 md:gap-6 border-solid ">
            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Treatment
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Value
              </p>
            </div>
          </div>

          <h2 className="text-lg md:text-xl font-semibold text-[#1E1E1E] my-4 md:mb-6">
            Sexual Habbits
          </h2>

          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 gap-4 md:gap-6 border-solid ">
            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Interest
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Function
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Satisfaction
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Birth Control Methods
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Condom Use
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Problems
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Concerns about HIV infection
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>
          </div>
        </div>

        {/* {Genital (Female)} */}
        <div className="w-full border border-[#E7E8E9] rounded-lg p-4 md:p-6">
          <h2 className="text-lg md:text-xl font-semibold text-[#1E1E1E] mb-4 md:mb-6">
            Genital (Female)
          </h2>

          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-2 gap-4 md:gap-6 border-solid ">
            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Age at Menarche
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                24
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Period Frequency
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Value
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Duration of periods
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Value
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Amount of bleeding:
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Value
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Period Regularity
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Value
              </p>
            </div>
            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Bleeding between periods
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Value
              </p>
            </div>
            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Bleeding after intercourse
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Value
              </p>
            </div>
            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Last menstrual period
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Value
              </p>
            </div>
            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Dysmenorrhea
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Value
              </p>
            </div>
            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Premenstrual tension
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Value
              </p>
            </div>
            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Age at Menopause
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Value
              </p>
            </div>
            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Menopausal symptoms
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Value
              </p>
            </div>
            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Post‐menopausal bleeding
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Value
              </p>
            </div>
            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Vaginal Discharge
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Value
              </p>
            </div>
          </div>
        </div>

        {/* {Musculoskeletal} */}
        <div className="w-full border border-[#E7E8E9] rounded-lg p-4 md:p-6">
          <h2 className="text-lg md:text-xl font-semibold text-[#1E1E1E] mb-4 md:mb-6">
            Musculoskeletal
          </h2>

          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-2 gap-4 md:gap-6 border-solid ">
            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Muscle or joint pain
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Stiffness
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Arthritis
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                If present, describe location of affectedjoints/muscles:
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Value
              </p>
            </div>
          </div>

          <h2 className="text-md md:text-lg font-semibold text-[#1E1E1E] my-4 md:mb-6">
            Timing of Symptoms
          </h2>

          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-2 gap-4 md:gap-6 border-solid ">
            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Morning
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Evening
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>
          </div>
        </div>

        {/* {Neurologic} */}
        <div className="w-full border border-[#E7E8E9] rounded-lg p-4 md:p-6">
          <h2 className="text-lg md:text-xl font-semibold text-[#1E1E1E] mb-4 md:mb-6">
            Neurologic
          </h2>

          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-2 gap-4 md:gap-6 border-solid ">
            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Change in mood
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Change in attention
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Change in memory
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Headache
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Seizures
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>
          </div>
        </div>

        {/* {Endocrine} */}
        <div className="w-full border border-[#E7E8E9] rounded-lg p-4 md:p-6">
          <h2 className="text-lg md:text-xl font-semibold text-[#1E1E1E] mb-4 md:mb-6">
            Endocrine
          </h2>

          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-2 gap-4 md:gap-6 border-solid ">
            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Thyroid trouble
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Heat intolerance
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Cold intolerance
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Excessive sweating
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Excessive thirst/hunger
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Polyuria
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>
          </div>
        </div>

        {/* {Psychiatric} */}
        <div className="w-full border border-[#E7E8E9] rounded-lg p-4 md:p-6">
          <h2 className="text-lg md:text-xl font-semibold text-[#1E1E1E] mb-4 md:mb-6">
            Psychiatric
          </h2>

          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-2 gap-4 md:gap-6 border-solid ">
            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Nervousness
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Tension
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Depression
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Memory changes
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Suicidal thoughts/plans
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Past counseling
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Psychiatric medications
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ROSFilled;
