import { useState, useEffect } from "react";
import { useLocation } from "react-router-dom";
import { Loader2 } from "lucide-react";
import { getRosFormByAppointmentId_apiCalls } from "../../api/api_calls/appointmentforms/rosForm_apiCalls";

const ROSFilled = ({ appointmentId }) => {
  const [loading, setLoading] = useState(true);
  const [rosData, setRosData] = useState(null);
  const location = useLocation();

  // Get appointmentId from props or location state as fallback
  const finalAppointmentId = appointmentId ||
    location.state?.appointmentId ||
    location.state?.appointment?.id;

  // Fetch ROS form data
  useEffect(() => {
    const fetchRosForm = async () => {
      if (!finalAppointmentId) {
        console.log("No appointment ID provided");
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        console.log("Fetching ROS form for appointmentId:", finalAppointmentId);

        const response = await getRosFormByAppointmentId_apiCalls({
          appointmentId: finalAppointmentId,
        });

        console.log("ROS form API response:", response);

        if (response?.data) {
          setRosData(response.data);
          console.log("ROS form data loaded:", response.data);
        } else {
          console.log("No ROS form found for this appointment");
        }
      } catch (error) {
        console.error("Failed to fetch ROS form:", error);
        // Don't set error state, just log it
      } finally {
        setLoading(false);
      }
    };

    fetchRosForm();
  }, [finalAppointmentId]);

  // Helper function to format boolean values
  const formatBoolean = (value) => {
    if (value === true) return "Yes";
    if (value === false) return "No";
    return "N/A";
  };

  // Helper function to format string values
  const formatString = (value) => {
    return value && value.trim() !== "" ? value : "N/A";
  };

  // Loading state
  if (loading) {
    return (
      <div className="flex justify-center items-center py-8">
        <Loader2 className="h-6 w-6 animate-spin mr-2" />
        <span>Loading ROS form...</span>
      </div>
    );
  }

  // No data state
  if (!rosData) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">No Review of Systems form found for this appointment.</p>
      </div>
    );
  }

  return (
    <div className="flex justify-center w-full ">
      <div className="w-full space-y-6">
        {/* General */}
        <div className="w-full border border-[#E7E8E9] rounded-lg p-4 md:p-6">
          <h2 className="text-lg md:text-xl font-semibold text-[#1E1E1E] mb-4 md:mb-6">
            General
          </h2>

          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 gap-4 md:gap-6">
            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Recent weight change
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                {formatBoolean(rosData.recentWeightChange)}
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Weakness
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                {formatBoolean(rosData.weakness)}
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Fatigue
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                {formatBoolean(rosData.fatigue)}
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Fever
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                {formatBoolean(rosData.fever)}
              </p>
            </div>
          </div>
        </div>

        {/* Head, Eyes, Ears, Nose , Throat  (HEENT) */}
        <div className="w-full border border-[#E7E8E9] rounded-lg p-4 md:p-6">
          <h2 className="text-lg md:text-xl font-semibold text-[#1E1E1E] mb-4 md:mb-6">
            Head, Eyes, Ears, Nose , Throat (HEENT)
          </h2>

          <h2 className="text-lg md:text-xl font-semibold text-[#1E1E1E] mt-4">
            Head
          </h2>

          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 gap-4 md:gap-6 border-solid border-b-[1px] py-4 border-b-[#E7E8E9]">
            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Head injury
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                {formatBoolean(rosData.headInjury)}
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Lightheadedness
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                {formatBoolean(rosData.lightheadedness)}
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Dizziness
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                {formatBoolean(rosData.dizziness)}
              </p>
            </div>
          </div>

          <h2 className="text-lg md:text-xl font-semibold text-[#1E1E1E] mt-4">
            Eyes
          </h2>

          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 gap-4 md:gap-6 border-solid border-b-[1px] py-4 border-b-[#E7E8E9]">
            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Vision change
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Glasses or contact lenses
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Pain
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Redness
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Excessive tearing
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Spots
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Double or blurred vision
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Specks
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Glaucoma
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Flashing lights
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Cataracts
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>
          </div>

          <h2 className="text-lg md:text-xl font-semibold text-[#1E1E1E] mt-4">
            Ears
          </h2>

          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 gap-4 md:gap-6 border-solid border-b-[1px] py-4 border-b-[#E7E8E9]">
            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Hearing loss
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Ringing in Ear(s)/Tinnitus
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Vertigo
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Earaches
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Infection
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Discharge
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                If hearing is decreased…
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Use /non‐ use of hearing aids{" "}
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>
          </div>

          <h2 className="text-lg md:text-xl font-semibold text-[#1E1E1E] mt-4">
            Nose and Sinuses
          </h2>

          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 gap-4 md:gap-6 py-4 border-solid border-b-[1px] border-b-[#E7E8E9]">
            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Frequent colds
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Nasal stuffiness
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Discharge
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Itching
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Hayfever
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Nosebleeds
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Sinus pressure/pain
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>
          </div>

          <h2 className="text-lg md:text-xl font-semibold text-[#1E1E1E] mt-4">
            Throat (Mouth & Pharynx)
          </h2>

          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 gap-4 md:gap-6 py-4 ">
            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Condition of teeth and gums
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Gum problems/ Bleeding
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Dentures(if any)
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>
          </div>

          <h2 className="text-md md:text-lg font-semibold text-[#1E1E1E] mt-4">
            Fit of dentures (if any)
          </h2>

          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 gap-4 md:gap-6 py-4">
            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Sore tongue
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Dry mouth
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Hoarseness
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Frequent sore throats
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>
          </div>
        </div>

        {/* {Neck} */}
        <div className="w-full border border-[#E7E8E9] rounded-lg p-4 md:p-6">
          <h2 className="text-lg md:text-xl font-semibold text-[#1E1E1E] mb-4 md:mb-6">
            Neck
          </h2>

          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 gap-4 md:gap-6 py-4">
            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Swollen glands
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Thyroid problems
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Goiter
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Lumps
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Pain or stiffness
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>
          </div>
        </div>

        {/* {Breasts} */}
        <div className="w-full border border-[#E7E8E9] rounded-lg p-4 md:p-6">
          <h2 className="text-lg md:text-xl font-semibold text-[#1E1E1E] mb-4 md:mb-6">
            Breasts
          </h2>

          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 gap-4 md:gap-6  py-4 ">
            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Nipple discharge
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Pain or discomfort
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Lumps
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Self‐examination practices
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>
          </div>
        </div>

        {/* {Respiratory } */}
        <div className="w-full border border-[#E7E8E9] rounded-lg p-4 md:p-6">
          <h2 className="text-lg md:text-xl font-semibold text-[#1E1E1E] mb-4 md:mb-6">
            Respiratory
          </h2>

          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 gap-4 md:gap-6 border-solid ">
            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Cough
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Sputum
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Lumps
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>
          </div>

          <h2 className="text-md md:text-lg font-semibold text-[#1E1E1E] my-4 md:mb-6">
            Sputum Details:
          </h2>

          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 gap-4 md:gap-6 border-solid ">
            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Color:
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Value
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Quantity:
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Value
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Presence of blood
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Shortness of breath (Dyspnea)
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Wheezing
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Pain with a deep breath (Pleuritic pain)
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Asthma
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Bronchitis
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Emphysema
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Pneumonia
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Tuberculosis
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>
          </div>
        </div>

        {/* {Gastrointestinal } */}
        <div className="w-full border border-[#E7E8E9] rounded-lg p-4 md:p-6">
          <h2 className="text-lg md:text-xl font-semibold text-[#1E1E1E] mb-4 md:mb-6">
            Gastrointestinal
          </h2>

          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 gap-4 md:gap-6 border-solid ">
            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Unintentional Weight loss/ gain
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Trouble swallowing
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Heartburn
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Appetite
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Trouble swallowing
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                On Special Diet
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Nausea
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>
          </div>

          <h2 className="text-md md:text-lg font-semibold text-[#1E1E1E] my-4 md:mb-6">
            Bowl Movement:
          </h2>

          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 gap-4 md:gap-6 border-solid ">
            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Stool Color:
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Value
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Size:
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Value
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Presence of blood
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Change in bowel habits
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Pain with Defecation
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Rectal bleeding
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Black or tarry stools
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Hemorrhoids
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Constipation
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Diarrhea / Irritable Bowel Syndrome
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Abdominal pain
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Food intolerance
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>
            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Excessive belching or passing of gas
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>
            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Jaundice (yellowing of skin)
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>
            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Hepatitis
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>
            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Liver or gallbladder trouble
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>
          </div>
        </div>

        {/* { Peripheral Vascular} */}
        <div className="w-full border border-[#E7E8E9] rounded-lg p-4 md:p-6">
          <h2 className="text-lg md:text-xl font-semibold text-[#1E1E1E] mb-4 md:mb-6">
            Peripheral Vascular
          </h2>

          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 gap-4 md:gap-6 border-solid ">
            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Intermittent leg pain with exertion (claudication)
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Leg cramps
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Varicose Veins
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Past clots in the veins
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Swelling in the calves, legs, or feet
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Color change in fingertips/toes during cold weather
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Swelling with redness/tenderness
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>
          </div>
        </div>

        {/* {Urinary} */}
        <div className="w-full border border-[#E7E8E9] rounded-lg p-4 md:p-6">
          <h2 className="text-lg md:text-xl font-semibold text-[#1E1E1E] mb-4 md:mb-6">
            Urinary
          </h2>

          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 gap-4 md:gap-6 border-solid ">
            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Frequency of Urination
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Polyuria
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Nocturia
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Urgency
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Burning or pain during urination
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Urinary infections
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Blood in the urine (hematuria)
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Kidney or flank pain
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Kidney stones
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Ureteral colic
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Suprapubic pain
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Incontinence
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Reduced caliber or force of the urinary system
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Hesitancy
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Dribbling
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>
          </div>
        </div>

        {/* {Genital (Male)} */}
        <div className="w-full border border-[#E7E8E9] rounded-lg p-4 md:p-6">
          <h2 className="text-lg md:text-xl font-semibold text-[#1E1E1E] mb-4 md:mb-6">
            Genital (Male)
          </h2>

          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-2 gap-4 md:gap-6 border-solid ">
            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Hernias
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Discharge from sores on the penis
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Testicular pain or masses
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Scrotal pain or swelling
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>
          </div>

          <h2 className="text-lg md:text-xl font-semibold text-[#1E1E1E] my-4 md:mb-6">
            History of Sexually Transmitted infections
          </h2>

          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 gap-4 md:gap-6 border-solid ">
            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Treatment
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Value
              </p>
            </div>
          </div>

          <h2 className="text-lg md:text-xl font-semibold text-[#1E1E1E] my-4 md:mb-6">
            Sexual Habbits
          </h2>

          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 gap-4 md:gap-6 border-solid ">
            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Interest
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Function
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Satisfaction
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Birth Control Methods
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Condom Use
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Problems
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Concerns about HIV infection
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>
          </div>
        </div>

        {/* {Genital (Female)} */}
        <div className="w-full border border-[#E7E8E9] rounded-lg p-4 md:p-6">
          <h2 className="text-lg md:text-xl font-semibold text-[#1E1E1E] mb-4 md:mb-6">
            Genital (Female)
          </h2>

          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-2 gap-4 md:gap-6 border-solid ">
            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Age at Menarche
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                24
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Period Frequency
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Value
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Duration of periods
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Value
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Amount of bleeding:
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Value
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Period Regularity
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Value
              </p>
            </div>
            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Bleeding between periods
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Value
              </p>
            </div>
            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Bleeding after intercourse
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Value
              </p>
            </div>
            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Last menstrual period
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Value
              </p>
            </div>
            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Dysmenorrhea
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Value
              </p>
            </div>
            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Premenstrual tension
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Value
              </p>
            </div>
            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Age at Menopause
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Value
              </p>
            </div>
            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Menopausal symptoms
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Value
              </p>
            </div>
            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Post‐menopausal bleeding
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Value
              </p>
            </div>
            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Vaginal Discharge
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Value
              </p>
            </div>
          </div>
        </div>

        {/* {Musculoskeletal} */}
        <div className="w-full border border-[#E7E8E9] rounded-lg p-4 md:p-6">
          <h2 className="text-lg md:text-xl font-semibold text-[#1E1E1E] mb-4 md:mb-6">
            Musculoskeletal
          </h2>

          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-2 gap-4 md:gap-6 border-solid ">
            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Muscle or joint pain
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Stiffness
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Arthritis
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                If present, describe location of affectedjoints/muscles:
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Value
              </p>
            </div>
          </div>

          <h2 className="text-md md:text-lg font-semibold text-[#1E1E1E] my-4 md:mb-6">
            Timing of Symptoms
          </h2>

          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-2 gap-4 md:gap-6 border-solid ">
            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Morning
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Evening
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>
          </div>
        </div>

        {/* {Neurologic} */}
        <div className="w-full border border-[#E7E8E9] rounded-lg p-4 md:p-6">
          <h2 className="text-lg md:text-xl font-semibold text-[#1E1E1E] mb-4 md:mb-6">
            Neurologic
          </h2>

          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-2 gap-4 md:gap-6 border-solid ">
            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Change in mood
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Change in attention
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Change in memory
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Headache
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Seizures
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>
          </div>
        </div>

        {/* {Endocrine} */}
        <div className="w-full border border-[#E7E8E9] rounded-lg p-4 md:p-6">
          <h2 className="text-lg md:text-xl font-semibold text-[#1E1E1E] mb-4 md:mb-6">
            Endocrine
          </h2>

          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-2 gap-4 md:gap-6 border-solid ">
            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Thyroid trouble
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Heat intolerance
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Cold intolerance
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Excessive sweating
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Excessive thirst/hunger
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Polyuria
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>
          </div>
        </div>

        {/* {Psychiatric} */}
        <div className="w-full border border-[#E7E8E9] rounded-lg p-4 md:p-6">
          <h2 className="text-lg md:text-xl font-semibold text-[#1E1E1E] mb-4 md:mb-6">
            Psychiatric
          </h2>

          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-2 gap-4 md:gap-6 border-solid ">
            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Nervousness
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Tension
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Depression
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Memory changes
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                Yes
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Suicidal thoughts/plans
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Past counseling
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>

            <div className="space-y-1">
              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                Psychiatric medications
              </h3>
              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                No
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ROSFilled;
